export const GetMultiPackagesResponse = {
  packageContainers: [
    {
      package: {
        created: '2021-04-28T21:10:36.153707367Z',
        updated: '2021-04-28T21:10:37.837816784Z',
        packageId: 'SOL-29fe4877-68fa-4799-8d2a-b827b9afe80a',
        partnerId: 'VUNI',
        marketId: 'default',
        name: 'WordPress Hosting',
        icon: '//lh3.googleusercontent.com/Ox_X7qXg4DyfK-XXTcE6uwqNwGK0f6RLmvdqiS9rFUBa5F7_3lDjcutobo9pRZje0mS-EuUK7yzF8gkylKofE0xJ902poHv9Zj0',
        status: 'PUBLISHED',
        tagline: 'Cloud hosted WordPress web sites',
        products: ['MP-9cc9f21f0a234a46ad78087fc09f16bc'],
        pricing: {
          prices: [
            {
              price: 10000,
            },
          ],
        },
        productOrder: ['MP-9cc9f21f0a234a46ad78087fc09f16bc'],
        lineItems: {
          lineItems: [
            {
              id: 'MP-9cc9f21f0a234a46ad78087fc09f16bc',
              quantity: '1',
            },
          ],
        },
        marketAction: {
          orderForm: {},
        },
        usesBillingPricing: true,
      },
    },
    {
      package: {
        created: '2021-06-07T18:21:56.964334317Z',
        updated: '2022-01-17T16:54:45.177685036Z',
        packageId: 'SOL-ae7a7081-90b7-47ea-9f10-c7a2b4028b3d',
        partnerId: 'VUNI',
        marketId: 'default',
        name: 'Task Manager',
        icon: 'https://lh3.googleusercontent.com/x4bWnrLZrHkYCbGbtVDAOBblcmT2XRTJ7PhOMCJ1Ojfep2hv_XFdpEnCUzoRr1fcAv8YAXpp6k2-dhXrxQ7DSpS9d5Px_4UWGI8BD-Kq',
        status: 'ARCHIVED',
        headerImageUrl:
          'https://lh3.googleusercontent.com/2olzNKtT1cFjLKSObbx937D3c3qFL-OYKPhTIVmzCJUAjuGGbcI9O4Dah_z0HKwd399J0Cpj6EwJT5KMVjxfLmPg44C5S3D0WhvM-bvE',
        tagline:
          'Grow revenue by providing visibility, review and social media services to your clients from one scaleable interface.',
        products: ['MP-R7G3NP55T725DM5VGDBHTWVT8L73MKXN'],
        hideProductIconsAndNames: true,
        pricing: {
          prices: [{}],
        },
        productOrder: ['MP-R7G3NP55T725DM5VGDBHTWVT8L73MKXN'],
        lineItems: {
          lineItems: [
            {
              id: 'MP-R7G3NP55T725DM5VGDBHTWVT8L73MKXN',
              quantity: '1',
            },
          ],
        },
        marketAction: {
          orderForm: {},
        },
        usesBillingPricing: true,
      },
    },
    {
      package: {
        created: '2020-07-15T21:15:43.546152250Z',
        updated: '2020-07-15T21:15:43.782504103Z',
        packageId: 'SOL-5805a88f-320c-40bd-8b2c-64d48cd3d2d5',
        partnerId: 'VUNI',
        marketId: 'default',
        name: 'copy-test',
        status: 'PUBLISHED',
        products: ['RM', 'SM'],
        pricing: {
          prices: [
            {
              price: 10000,
              frequency: 'ONCE',
            },
          ],
        },
        productOrder: ['RM', 'SM'],
        lineItems: {
          lineItems: [
            {
              id: 'RM',
              quantity: '1',
            },
            {
              id: 'SM',
              quantity: '1',
            },
          ],
        },
        marketAction: {
          contactForm: {},
        },
      },
    },
  ],
};
