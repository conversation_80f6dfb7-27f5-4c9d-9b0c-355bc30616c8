import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { RouteParamsService } from '@vendasta/route-params';
import { SessionAuthGuard } from './core/auth-guards/session-auth-guard.service';
import { EmptyComponent, LocalLoginResolver, LoginComponent } from './login/login.component';
import { TaskAttachmentPreviewComponent } from './tasks/task-common/task-details/task-attachments/task-attachment-preview.component';

const appRoutes: Routes = [
  {
    // This should only be hit locally. ARM will handle top level routing for production.
    path: '',
    resolve: { switch: LocalLoginResolver },
    component: EmptyComponent,
  },
  { path: 'ng/login', pathMatch: 'full', component: LoginComponent },
  { path: 'partner/:partnerId', pathMatch: 'full', redirectTo: 'partner/:partnerId/tasks/task-list' },
  { path: 'partner/:partnerId/tasks', pathMatch: 'full', redirectTo: 'partner/:partnerId/tasks/task-list' },
  {
    path: 'partner/:partnerId',
    canActivate: [SessionAuthGuard],
    canActivateChild: [SessionAuthGuard],
    resolve: { routeParams: RouteParamsService },
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    loadChildren: () => import('./base/base.module').then((m) => m.BaseModule),
  },
  // Handle Redirects from bookmarked ARM routes
  {
    path: ':partnerId/accounts/:accountGroupId/edit-recurring-task/:recurringTaskId',
    redirectTo: 'partner/:partnerId/accounts/:accountGroupId/edit-recurring-task/:recurringTaskId',
  },
  // [PION-45] Redirect old The Loop links to new design.
  {
    path: ':partnerId/accounts/:accountGroupId',
    redirectTo: 'partner/:partnerId/accounts/:accountGroupId',
  },
  // legacy route redirects
  {
    path: ':partnerId',
    redirectTo: 'partner/:partnerId/tasks/task-list',
    pathMatch: 'full',
  },
  {
    path: ':partnerId/tasks/preview',
    pathMatch: 'full',
    redirectTo: 'partner/:partnerId/tasks/task-list',
  },
  {
    path: ':partnerId/tasks/task-list',
    redirectTo: 'partner/:partnerId/tasks/task-list',
  },
  {
    path: ':partnerId/tasks/calendar',
    pathMatch: 'full',
    redirectTo: 'partner/:partnerId/tasks/calendar',
  },
  {
    path: ':partnerId/projects',
    redirectTo: 'partner/:partnerId/projects',
    pathMatch: 'full',
  },
  {
    path: ':partnerId/projects/create',
    redirectTo: 'partner/:partnerId/projects/create',
    pathMatch: 'full',
  },
  {
    path: ':partnerId/projects/:accountGroupId/create',
    redirectTo: 'partner/:partnerId/projects/:accountGroupId/create',
    pathMatch: 'full',
  },
  { path: ':partnerId/templates', pathMatch: 'full', redirectTo: 'partner/:partnerId/templates' },
  { path: ':partnerId/templates/edit/:templateId', redirectTo: 'partner/:partnerId/templates/edit/:templateId' },
  { path: ':partnerId/templates/create', redirectTo: 'partner/:partnerId/templates/create' },
  { path: ':partnerId/templates/library', redirectTo: 'partner/:partnerId/templates/library' },
  {
    path: ':partnerId/templates/library/create/:templateId',
    redirectTo: 'partner/:partnerId/templates/library/create/:templateId',
  },

  {
    path: ':partnerId/my-profile',
    redirectTo: 'partner/:partnerId/my-profile',
  },

  {
    path: ':partnerId/tasks/attachment/:sharedSecret/:fileName',
    resolve: { routeParams: RouteParamsService },
    canActivate: [SessionAuthGuard],
    canActivateChild: [SessionAuthGuard],
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    component: TaskAttachmentPreviewComponent,
  },
  {
    path: 'inbox',
    outlet: 'inbox',
    loadChildren: () => import('@galaxy/conversation/inbox').then((m) => m.INBOX_ROUTES),
  },
  {
    path: 'conversation/:id',
    outlet: 'inbox',
    loadChildren: () => import('@galaxy/conversation/inbox').then((m) => m.INBOX_CONVERSATION_OVERLAY),
  },
];

@NgModule({
  imports: [RouterModule.forRoot(appRoutes, {})],
  exports: [RouterModule],
})
export class AppRoutingModule {}
