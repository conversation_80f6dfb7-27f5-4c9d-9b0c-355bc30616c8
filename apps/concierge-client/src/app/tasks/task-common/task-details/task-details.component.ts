import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  Inject,
  Input,
  OnDestroy,
  OnInit,
  Output,
  signal,
  ViewChild,
  WritableSignal,
} from '@angular/core';
import { AtlasDataService } from '@galaxy/atlas/core';
import { FeatureFlagStatusInterface } from '@galaxy/partner';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { FileInfo } from '@vendasta/galaxy/uploader';
import { PersonaType } from '@vendasta/iam';
import {
  DomainKeyValue,
  KeyValueTypes,
  SearchTaskResponseInterface,
  Status,
  TaskInterface,
  TaskSdkService,
} from '@vendasta/task';
import {
  AccountGroup,
  AccountsSdkService,
  AuxiliaryDataFieldSchema,
  ConciergeAccount,
  CustomTag,
  TaskAuxiliaryFieldDataApiService,
  TaskAuxiliaryFieldSchemaApiService,
  TaskIdentityInterface,
} from '@vendasta/task-manager';
import { productCanSso } from 'marketplace-ui';
import { BehaviorSubject, combineLatest, iif, Observable, of, Subscription } from 'rxjs';
import { filter, finalize, map, switchMap, take, tap } from 'rxjs/operators';
import { ConciergeAccountApiService } from '../../../business-common/concierge-account/concierge-account-api.service';
import { OverduePipe } from '../../../business-common/pipes/overdue.pipe';
import { DecryptionService } from '../../../common/decryption.service';
import { SidenavService } from '../../../common/sidenav.service';
import { UserNotificationControlService } from '../../../common/user-notification-control.service';
import { ConfirmationDialogService } from '../../../confirmation-dialog/confirmation-dialog.service';
import {
  DIGITAL_AGENTS_INJECTION_TOKEN,
  FEATURE_FLAG_INJECTION_TOKEN,
  FULFILLMENT_TEAM_NOTES,
  HAS_CUSTOM_FIELDS_INJECTION_TOKEN,
  MAX_ACCOUNT_NOTE_LENGTH,
  NEXT_TASK_DEPENDS,
  PARTNER_ID_INJECTION_TOKEN,
  PARTNER_STATUSES_INJECTION_TOKEN,
  PRIVATE_NOTES,
  STRING_FIELD_LENGTH_LIMIT,
  TASK_DEPENDS_ON,
  TaskTags,
  UNASSIGNED_USER,
} from '../../../core/constants';
import { DependentSubtaskService } from '../../../core/dependent-subtask.service';
import { DigitalAgent } from '../../../core/digital-agents/DigitalAgent';
import { COMMENTS_TAB_FIRST_FEATURE } from '../../../core/feature-flag/featureflags';
import { PendingChangesComponent } from '../../../core/guards/pending-changes-guard.service';
import { tasktoHydratedTask } from '../../../core/hydrated-task-utils';
import { ProductSsoService } from '../../../core/product-sso.service';
import { TaskListService, TaskTableRow } from '../../../core/task-list.service';
import { UserService } from '../../../core/user.service';
import { SelectInterface } from '../../../inline-edit-fields/display-select/display-select.component';
import { DateOutput } from '../../../inline-edit-fields/outputs';
import { EditEmitterInterface } from '../../../inline-edit-fields/state';
import { PartnerStatuses } from '../../../partner/partner';
import { FulfillmentProductKeys } from '../../core/constants';
import { FulfillingProductData, SubtaskProductService } from '../../subtask/subtask-product.service';
import { TaskApiService } from '../../task-api.service';

interface TaskAccountAndPartner {
  accountGroupId: string;
  partnerId: string;
}

@Component({
  selector: 'app-task-details',
  templateUrl: './task-details.component.html',
  styleUrls: ['./task-details.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [TaskListService, OverduePipe, SubtaskProductService],
  standalone: false,
})
export class TaskDetailsComponent extends PendingChangesComponent implements OnInit, OnDestroy {
  @Input() set taskData(taskData: TaskInterface) {
    this.account$$.next(null);
    this.inputTask$$.next(taskData);
    const taskPartnerId: string = taskData?.identity?.namespace?.split('/')[1];
    const fulfillingPartnerId: string = TaskApiService.getFulfillingPartnerIdFromMetadata(taskData.metadata);
    if (fulfillingPartnerId && fulfillingPartnerId !== taskPartnerId) {
      this.suffixPartnerId = taskPartnerId;
    }
    this.completeIdentity = taskData?.identity?.namespace + taskData.identity?.parentPath + taskData?.identity?.taskId;
  }

  @Input() isSidePanel = true;
  @Output() taskUpdated = new EventEmitter<TaskInterface>();
  @ViewChild('taskTitle') taskTitle: ElementRef;
  dependentTaskList$: Observable<TaskInterface[]>;
  statusOptions$: Observable<SelectInterface[]>;
  assigneeOptions$: Observable<SelectInterface[]>;
  taskAccountAndPartner$: Observable<TaskAccountAndPartner>;
  currentTaskTableRow$$: BehaviorSubject<TaskTableRow> = new BehaviorSubject<TaskTableRow>(null);
  subscriptions: Subscription[] = [];
  accountGroupMap$: Observable<Map<string, AccountGroup>>;
  parentTaskMap$: Observable<Map<string, TaskInterface>>;
  customTagsMap$: Observable<Map<string, CustomTag[]>>;
  account$$: BehaviorSubject<ConciergeAccount> = new BehaviorSubject<ConciergeAccount>(null);
  fulfillmentProduct$$: BehaviorSubject<FulfillingProductData> = new BehaviorSubject<FulfillingProductData>(null);
  privateNotes$: Observable<string>;
  fulfillmentTeamNotes$: Observable<string>;
  fulfillmentTeamNotesVisible$: Observable<boolean>;
  inputTask$$: BehaviorSubject<TaskInterface> = new BehaviorSubject<TaskInterface>(null);
  currentDate: Date = new Date();
  readonly maxAccountNoteLength = MAX_ACCOUNT_NOTE_LENGTH;

  readonly updatedTaskStatus$$: BehaviorSubject<string> = new BehaviorSubject<string>(null);

  readonly isEditingTitle$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  readonly isSaving$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  maxNotesCharacters = STRING_FIELD_LENGTH_LIMIT;
  Status = Status;
  suffixPartnerId: string;
  productCanSso = productCanSso;
  commentsTabFirst$: Observable<boolean>;
  isImpersonating$: Observable<boolean>;
  #pendingChanges = false;
  accountNotesModified: boolean;
  publicNotesModified: boolean;
  privateNotesModified: boolean;
  descriptionModified: boolean;
  isReviewEdited$: Observable<boolean>;
  selectedFields: WritableSignal<AuxiliaryDataFieldSchema[]> = signal([]);
  isPartnerAdmin$: Observable<boolean>;
  completeIdentity: string;

  protected readonly FULFILLMENT_TEAM_NOTES = FULFILLMENT_TEAM_NOTES;
  protected readonly PRIVATE_NOTES = PRIVATE_NOTES;

  constructor(
    @Inject(PARTNER_ID_INJECTION_TOKEN) public partnerId$: Observable<string>,
    @Inject(DIGITAL_AGENTS_INJECTION_TOKEN) public assignees$: Observable<DigitalAgent[]>,
    @Inject(PARTNER_STATUSES_INJECTION_TOKEN) private partnerStatuses$: Observable<PartnerStatuses>,
    @Inject(HAS_CUSTOM_FIELDS_INJECTION_TOKEN) public hasCustomFields$: Observable<boolean>,
    private snackbarService: SnackbarService,
    private taskListService: TaskListService,
    private accountService: ConciergeAccountApiService,
    private decryptionService: DecryptionService,
    private userNotificationControlService: UserNotificationControlService,
    private dependentService: DependentSubtaskService,
    private taskApiService: TaskApiService,
    @Inject(FEATURE_FLAG_INJECTION_TOKEN) private featureFlags$: Observable<FeatureFlagStatusInterface>,
    private taskManagerAccountService: AccountsSdkService,
    private atlasDataService: AtlasDataService,
    private productSsoService: ProductSsoService,
    private confirmationDialogService: ConfirmationDialogService,
    private sidenavService: SidenavService,
    private taskAuxiliaryService: TaskAuxiliaryFieldDataApiService,
    private taskAuxiliarySchemaService: TaskAuxiliaryFieldSchemaApiService,
    private userService: UserService,
  ) {
    super();
  }

  ngOnInit(): void {
    this.commentsTabFirst$ = this.featureFlags$.pipe(map((f) => f[COMMENTS_TAB_FIRST_FEATURE]));

    this.assigneeOptions$ = this.assignees$.pipe(
      map((assignee) => assignee.map((da) => ({ id: da.subjectId, text: da.getName(), iconUrl: da.profilePicture }))),
    );
    this.statusOptions$ = this.partnerStatuses$.pipe(
      map((s) => s.all_statuses.map((status) => ({ id: status, text: status }))),
    );
    this.taskAccountAndPartner$ = this.inputTask$$.pipe(
      map((task) => {
        return {
          accountGroupId: task.identity.namespace.split('/')[3],
          partnerId: task.identity.namespace.split('/')[1],
        };
      }),
    );
    this.subscriptions.push(
      this.inputTask$$
        .pipe(switchMap((task) => this.taskManagerAccountService.getConciergeAccountForTask(task.identity)))
        .subscribe((account) => this.account$$.next(account)),
    );
    this.subscriptions.push(
      this.inputTask$$
        .pipe(map((task) => this.getFulfillingProduct(task)))
        .subscribe((product) => this.fulfillmentProduct$$.next(product)),
    );
    this.fulfillmentTeamNotesVisible$ = combineLatest([this.partnerId$, this.inputTask$$]).pipe(
      map(([partnerId, task]) => {
        if (!task) {
          return false;
        }
        const fulfillingPartnerId = TaskApiService.getFulfillingPartnerIdFromMetadata(task.metadata);
        const taskPartnerId = TaskApiService.getPartnerIdFromNamespace(task.identity.namespace);
        if (fulfillingPartnerId !== '' && fulfillingPartnerId === partnerId && fulfillingPartnerId !== taskPartnerId) {
          return true;
        }
      }),
    );
    this.accountGroupMap$ = this.inputTask$$.pipe(
      switchMap((task) => this.taskListService.getAccountGroupMap(of([task]))),
    );
    this.parentTaskMap$ = this.inputTask$$.pipe(
      switchMap((task) => this.taskListService.getParentTaskMap(of([tasktoHydratedTask(task)]))),
    );
    this.customTagsMap$ = this.inputTask$$.pipe(switchMap((task) => this.taskListService.getCustomTagsMap(of([task]))));
    this.dependentTaskList$ = this.inputTask$$.pipe(
      switchMap((task) =>
        iif(
          () => DomainKeyValue.fromKeyValue(task.data).get(NEXT_TASK_DEPENDS, KeyValueTypes.BOOL)[0] as boolean,
          this.taskApiService
            .search({
              namespace: TaskSdkService.buildAccountGroupNamespace(
                task.identity.namespace.split('/')[1],
                task.identity.namespace.split('/')[3],
              ),
              parentPathExact: task.identity.parentPath,
              cursor: '',
              pageSize: 100,
              access: [PersonaType.digital_agent],
            })
            .pipe(map((response: SearchTaskResponseInterface) => response.tasks)),
          of([]),
        ),
      ),
    );
    this.subscriptions.push(
      this.inputTask$$
        .pipe(switchMap(() => this.transformTask()))
        .subscribe((task) => this.currentTaskTableRow$$.next(task)),
    );
    this.isImpersonating$ = this.atlasDataService.impersonateeUsername$.pipe(map(Boolean));

    this.isReviewEdited$ = this.inputTask$$.pipe(map((task) => task?.tags?.includes(TaskTags.REVIEW_EDITED)));

    this.partnerId$
      .pipe(
        switchMap((partnerId) =>
          this.taskAuxiliaryService.listAuxiliaryData({
            auxiliaryDataObjectId: {
              partnerId: partnerId,
              objectId: this.completeIdentity,
            },
            pagingOptions: {
              pageSize: 100,
              cursor: '',
            },
          }),
        ),
        take(1),
      )
      .subscribe((resp) => {
        const schema = resp.jsonSchema ? JSON.parse(resp.jsonSchema) : { properties: {} };
        const selectedFields: AuxiliaryDataFieldSchema[] = Object.entries(schema.properties).map(
          ([key, schemaValue]) => {
            return {
              fieldId: key,
              fieldName: schemaValue?.['title'],
              fieldDescription: schemaValue?.['description'],
            } as AuxiliaryDataFieldSchema;
          },
        );
        this.selectedFields.set(selectedFields);
      });

    this.isPartnerAdmin$ = this.userService.isPartnerCenterAdmin$;
  }

  private getFulfillingProduct(task: TaskInterface): FulfillingProductData {
    const product: FulfillingProductData = {
      productId: '',
      productIcon: '',
      productName: '',
      isActive: false,
    };
    if (task.data && task.data.keyValues) {
      product.productId = DomainKeyValue.fromKeyValue(task.data)
        ?.get(FulfillmentProductKeys.ID, KeyValueTypes.STRING)?.[0]
        ?.toString();
      product.productName = DomainKeyValue.fromKeyValue(task.data)
        ?.get(FulfillmentProductKeys.NAME, KeyValueTypes.STRING)?.[0]
        ?.toString();
      product.productIcon = DomainKeyValue.fromKeyValue(task.data)
        ?.get(FulfillmentProductKeys.ICON, KeyValueTypes.STRING)?.[0]
        ?.toString();
      product.isActive = true;
    }

    return product;
  }

  private transformTask(): Observable<TaskTableRow> {
    // similar to setupRows in task-list.component.ts
    return combineLatest([this.accountGroupMap$, this.parentTaskMap$, this.customTagsMap$]).pipe(
      filter(([agMap, pMap]) => Boolean(agMap && pMap)),
      map(
        ([agMap, parentTaskMap, customTagsMap]: [
          Map<string, AccountGroup>,
          Map<string, TaskInterface>,
          Map<string, CustomTag[]>,
        ]): TaskTableRow => {
          const task: TaskInterface = this.inputTask$$.getValue();
          const account = agMap.get(task.identity.namespace.split('/')[3]);
          const napData = account ? account.napData : null;
          const parts = task.identity.parentPath.split('/');
          const parentTaskId = parts[parts.length - 2];
          const parentTask = parentTaskMap.get(parentTaskId);
          let encryptedPrivateNotesValues: string[] = [];
          let encryptedFulfillmentTeamNotesValues: string[] = [];
          try {
            encryptedPrivateNotesValues = DomainKeyValue.fromKeyValue(task.data).get(
              PRIVATE_NOTES,
              KeyValueTypes.STRING,
            ) as string[];

            encryptedFulfillmentTeamNotesValues = DomainKeyValue.fromKeyValue(task.data).get(
              FULFILLMENT_TEAM_NOTES,
              KeyValueTypes.STRING,
            ) as string[];
          } catch (e) {
            // error with kv pair
          }
          // decrypt private notes
          if (encryptedPrivateNotesValues.length > 0 && encryptedPrivateNotesValues[0] !== '') {
            this.privateNotes$ = this.decryptNotes(encryptedPrivateNotesValues[0].toString(), task.identity);
          } else {
            this.privateNotes$ = of('');
          }
          // decrypt fulfillment team notes
          if (encryptedFulfillmentTeamNotesValues.length > 0 && encryptedFulfillmentTeamNotesValues[0] !== '') {
            this.fulfillmentTeamNotes$ = this.decryptNotes(
              encryptedFulfillmentTeamNotesValues[0].toString(),
              task.identity,
            );
          } else {
            this.fulfillmentTeamNotes$ = of('');
          }

          return {
            ...task,
            account: account,
            accountName: napData ? napData.companyName : '',
            address: napData ? napData.address + ' ' + napData.city + ', ' + napData.state + ', ' + napData.zip : '',
            parentName: parentTask ? parentTask.title : '',
            phoneNumber: napData && napData.workNumber ? napData.workNumber[0] : '',
            countryCode: napData ? napData.country : '',
            accountGroupId: account ? account.accountGroupId : task.identity.namespace.split('/')[3],
            parentTaskId: parentTask ? parentTask.identity.taskId : '',
            reviewData: this.taskListService.getReviewData(task),
            savingStatus$$: new BehaviorSubject<boolean>(false),
            customTagData: customTagsMap[task.identity.taskId],
          } as TaskTableRow;
        },
      ),
    );
  }

  handleStatusChange(status: EditEmitterInterface<string>, saving$$: BehaviorSubject<boolean>): void {
    const task: TaskTableRow = this.currentTaskTableRow$$.getValue();
    this.subscriptions.push(
      this.taskListService
        .setStatus(task.identity.namespace, task.identity.parentPath, task.identity.taskId, status.new, task.version)
        .pipe(
          tap(() => {
            if (
              status.new === Status.WaitingOnCustomer &&
              task.tags &&
              task.tags.includes(TaskTags.BUSINESS_CENTER_VISIBLE)
            ) {
              this.userNotificationControlService.showWaitingOnCustomerNotificationDialog(task);
            }
          }),
        )
        .subscribe({
          next: (newVersion) => {
            if (status.new === Status.Completed) {
              task.completionDate = this.currentDate;
              this.checkDependency();
            } else {
              task.completionDate = null;
            }
            task.version = newVersion;
            task.status = status.new;
            this.updatedTaskStatus$$.next(status.new);
            this.currentTaskTableRow$$.next(task);
            this.updateTaskState(task);
            this.snackbarService.openSuccessSnack(`Status has been set to ${status.new}`);
          },
          error: () => {
            this.snackbarService.openErrorSnack(`Failed to set status to ${status.new}`);
            saving$$.next(false);
          },
          complete: () => saving$$.next(false),
        }),
    );
  }

  checkDependency(): void {
    this.dependentTaskList$.pipe(take(1)).subscribe((tasks) => {
      const toUpdate = tasks.filter(
        (task) =>
          DomainKeyValue.fromKeyValue(task.data).get(TASK_DEPENDS_ON, KeyValueTypes.STRING)[0] ===
          this.currentTaskTableRow$$.getValue().identity.taskId,
      );
      toUpdate.forEach((task) => {
        this.dependentService.checkDependencyDisplay(task);
      });
    });
  }

  handleDueDateChange(dateObject: DateOutput): void {
    const task: TaskTableRow = this.currentTaskTableRow$$.getValue();
    this.subscriptions.push(
      this.taskListService
        .setDueDate(
          task.identity.namespace,
          task.identity.parentPath,
          task.identity.taskId,
          dateObject.new,
          task.version,
        )
        .subscribe({
          next: (newVersion) => {
            task.version = newVersion;
            task.dueDate = dateObject.new;
            this.currentTaskTableRow$$.next(task);
            this.updateTaskState(task);
            this.snackbarService.openSuccessSnack(`Due date has been updated to ${dateObject.new.toDateString()}`);
          },
          error: () =>
            this.snackbarService.openErrorSnack(`Failed to update due date to ${dateObject.new.toDateString()}`),
        }),
    );
  }

  handleAssigneeChange(assignee: EditEmitterInterface<string>): void {
    const task: TaskTableRow = this.currentTaskTableRow$$.getValue();
    if (assignee.new === assignee.old) {
      return;
    }
    if (assignee.new === UNASSIGNED_USER || assignee.new === '') {
      this.subscriptions.push(
        this.taskListService
          .removeAssignee(
            task.identity.namespace,
            task.identity.parentPath,
            task.identity.taskId,
            task.assignees[0],
            task.version,
          )
          .subscribe({
            next: (newVersion) => {
              task.version = newVersion;
              delete task.assignees;
              this.currentTaskTableRow$$.next(task);
              this.updateTaskState(task);
              this.snackbarService.openSuccessSnack(`Assignee has been updated`);
            },
            error: () => this.snackbarService.openErrorSnack(`Failed to update Assignee`),
          }),
      );
    } else {
      if (task.assignees === undefined) {
        task.assignees = [];
      }
      this.subscriptions.push(
        this.taskListService
          .setAssignee(
            task.identity.namespace,
            task.identity.parentPath,
            task.identity.taskId,
            assignee.new,
            task.version,
          )
          .subscribe({
            next: (newVersion) => {
              task.version = newVersion;
              task.assignees = [assignee.new];
              this.currentTaskTableRow$$.next(task);
              this.updateTaskState(task);
              this.snackbarService.openSuccessSnack(`Assignee has been updated`);
            },
            error: () => this.snackbarService.openErrorSnack(`Failed to update Assignee`),
          }),
      );
    }
  }

  handleAccountNotesChanged(newNote: string): void {
    combineLatest([this.account$$, this.taskAccountAndPartner$])
      .pipe(
        switchMap(([account, details]) => {
          if (account.notes === newNote) {
            return of(null);
          }
          return this.accountService.setNote(details.partnerId, details.accountGroupId, newNote);
        }),
        take(1),
      )
      .subscribe({
        next: (result) => {
          if (result) {
            this.snackbarService.openSuccessSnack('Account notes updated');
            this.updateTaskState();
          }
        },
        error: (err) => {
          console.log(err);
          this.snackbarService.openErrorSnack('Failed to update account notes');
        },
      });
  }

  handlePublicNotesChanged(newNotes: string): void {
    const task: TaskTableRow = this.currentTaskTableRow$$.getValue();
    this.subscriptions.push(
      this.taskListService
        .setNotes(task.identity.namespace, task.identity.parentPath, task.identity.taskId, newNotes, task.version)
        .subscribe({
          next: (newVersion: number) => {
            task.version = newVersion;
            task.notes = newNotes;
            this.snackbarService.openSuccessSnack('Public notes updated successfully');
            this.currentTaskTableRow$$.next(task);
            this.updateTaskState(task);
          },
          error: () => this.snackbarService.openErrorSnack('There was an error updating public notes'),
        }),
    );
  }

  handlePrivateNotesChanged(newNotes: string, key: string): void {
    const task: TaskTableRow = this.currentTaskTableRow$$.getValue();
    const taskData: DomainKeyValue = task.data ? DomainKeyValue.fromKeyValue(task.data) : new DomainKeyValue();
    this.subscriptions.push(
      this.decryptionService
        .encryptText(newNotes)
        .pipe(
          switchMap((encryptedNotes: string) => {
            taskData.set(key, [encryptedNotes], KeyValueTypes.STRING);
            return this.taskListService.setPrivateNotes(task.identity, encryptedNotes, task.version, key);
          }),
        )
        .subscribe({
          next: (newVersion: number) => {
            this.snackbarService.openSuccessSnack('Private notes updated successfully');
            task.data = taskData.toKeyValue();
            task.version = newVersion;
            this.currentTaskTableRow$$.next(task);
            this.updateTaskState(task);
          },
          error: () => this.snackbarService.openErrorSnack('There was an error updating private notes'),
        }),
    );
  }

  handleDescriptionChanged(newDescription: string): void {
    const task: TaskTableRow = this.currentTaskTableRow$$.getValue();
    this.subscriptions.push(
      this.taskListService
        .setDescription(
          task.identity.namespace,
          task.identity.parentPath,
          task.identity.taskId,
          newDescription,
          task.version,
        )
        .subscribe({
          next: (newVersion: number) => {
            task.description = newDescription;
            task.version = newVersion;
            this.snackbarService.openSuccessSnack('Description updated successfully');
            this.currentTaskTableRow$$.next(task);
            this.updateTaskState(task);
          },
          error: () => this.snackbarService.openErrorSnack('There was an error updating description'),
        }),
    );
  }

  handleEditClick(): void {
    this.isEditingTitle$$.next(true);
    window.setTimeout(() => {
      this.taskTitle.nativeElement.focus();
    }, 0);
  }

  handleUpdateTitle(value: string): void {
    const task: TaskTableRow = this.currentTaskTableRow$$.getValue();
    if (value === task.title) {
      this.isEditingTitle$$.next(false);
      return;
    }
    this.isSaving$$.next(true);
    this.taskListService
      .setTitle(task.identity.namespace, task.identity.parentPath, task.identity.taskId, value)
      .pipe(
        take(1),
        finalize(() => this.isSaving$$.next(false)),
      )
      .subscribe({
        next: (version) => {
          this.snackbarService.openSuccessSnack('Task title successfully updated to ' + value);
          task.title = value;
          task.version = version;
          this.currentTaskTableRow$$.next(task);
          this.isEditingTitle$$.next(false);
        },
        error: () => this.snackbarService.openErrorSnack('There was an error updating the task title'),
      });
  }

  decryptNotes(notes: string, identity: TaskIdentityInterface): Observable<string> {
    return this.decryptionService.decryptText(notes, identity);
  }

  updateTaskState(task?: TaskInterface): void {
    this.taskUpdated.emit(task || this.currentTaskTableRow$$.getValue());
  }

  handleFilesChanged(files: FileInfo[]): void {
    const currentTask: TaskTableRow = this.currentTaskTableRow$$.getValue();
    currentTask.attachment = files?.map((file) => {
      return {
        name: file.name,
        url: file.url,
      };
    });
    this.currentTaskTableRow$$.next(currentTask);
    this.updateTaskState(currentTask);
  }

  getCompletedOnDateText(): string {
    return (
      'Completed ' +
      this.currentTaskTableRow$$
        .getValue()
        .completionDate?.toLocaleString('default', { month: 'short', day: 'numeric' })
    );
  }

  handleCustomTagsUpdate(customTags: CustomTag[]): void {
    const task: TaskTableRow = this.currentTaskTableRow$$.getValue();
    task.customTagData = customTags;
    task.customTags = customTags.map((t) => t.tagId);
    this.currentTaskTableRow$$.next(task);
    this.updateTaskState(task);
  }

  ssoClicked(productId: string, accountGroupId: string): void {
    this.productSsoService
      .getProductEntryUrl(productId, accountGroupId)
      .pipe(take(1))
      .subscribe((url) => {
        window.open(url, '_blank', 'noopener');
      });
  }

  handleAccountNotesModified(value: boolean): void {
    this.accountNotesModified = value;
    this.updatePendingChanges();
  }

  handlePublicNotesModified(value: boolean): void {
    this.publicNotesModified = value;
    this.updatePendingChanges();
  }

  handlePrivateNotesModified(value: boolean): void {
    this.privateNotesModified = value;
    this.updatePendingChanges();
  }

  handleDescriptionModified(value: boolean): void {
    this.descriptionModified = value;
    this.updatePendingChanges();
  }

  updatePendingChanges(): void {
    this.#pendingChanges =
      this.accountNotesModified || this.publicNotesModified || this.privateNotesModified || this.descriptionModified;
    this.sidenavService.pendingChanges = this.#pendingChanges;
  }

  canDeactivate(): boolean {
    return !this.#pendingChanges;
  }

  pendingChangesDialog(): Observable<boolean> {
    return this.confirmationDialogService.leaveWithUnsavedChanges();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
}
