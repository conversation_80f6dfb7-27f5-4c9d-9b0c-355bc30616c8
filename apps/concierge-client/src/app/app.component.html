<inbox-inbox>
  <mat-sidenav-container class="sidenav-container" [hasBackdrop]="true" (backdropClick)="closeSideNav()">
    <mat-sidenav-content>
      <router-outlet></router-outlet>
    </mat-sidenav-content>
    <mat-sidenav
      [disableClose]="true"
      [fixedInViewport]="true"
      [mode]="(useSideNavOverMode$ | async) ? 'over' : 'side'"
      #panel
      class="panel"
      position="end"
    >
      <!-- we load different components dynamically -->
      <ng-container #content></ng-container>
    </mat-sidenav>
  </mat-sidenav-container>
</inbox-inbox>
