Business Center Mobile Versions
=================
This file tracks the version codes and version number for mobile app releasing.

Version Code should be a positive integer, it is for internal use only.

Version Name should be a string in the format of x.y.z, where x, y, z are non-negative integers.
Version Name is for public use, it is the version number that will be shown to the users.

After you updated this file, update both iOS and Android version using the following command:

```bash
npx capacitor-set-version -v <VersionName> -b <VersionCode>
```
Further information can be found in the [capacitor-set-version](https://www.npmjs.com/package/capacitor-set-version)


## Steps to update
Steps of app updating can be found in confluence: [Android](https://vendasta.jira.com/wiki/spaces/WT/pages/2413297841/Push+new+version+of+Business+App+in+Google+Play), [IOS](https://vendasta.jira.com/wiki/spaces/WT/pages/2420410222/iOS+-+Publish+mobile+app+to+Apple+TestFlight#Publishing-new-TestFlight-to-New-or-Existing-App)


## Versions


#### Version Code: 41 Version Name: 1.2.7
- Add CRM to funnel metric
- Minor bugs fix

#### Version Code: 40 Version Name: 1.2.6
- Sticky the header on all pages
- Fix user being logged out every 30 minutes

#### Version Code: 39 Version Name: 1.2.5
- Update for latest features

#### Version Code: 38 Version Name: 1.2.4
- Fix notification flyout

#### Version Code: 37 Version Name: 1.2.3
- Fix navigation missing

#### Version Code: 36 Version Name: 1.2.2
- Bug fixes and improvements

#### Version Code: 35 Version Name: 1.2.1
- Bug fixes and improvements

#### Version Code: 34 Version Name: 1.2.0
- Support multi-partner login for Broadly and Neighborly

#### Version Code: 32 Version Name: 1.1.7
- fix Review request modal scroll behavior

#### Version Code: 31 Version Name: 1.1.6
- fix push notification device registration
- hide inbox pro widget if inbox is deactivated
- AI tab items

#### Version Code: 30 Version Name: 1.1.5
- fix exec report header styling

#### Version Code: 29 Version Name: 1.1.4
- Skip consent screen for iOS

#### Version Code: 28 Version Name: 1.1.3
- Fix crm ui issues
- Fix integration page open in browser
- Get start page refactor
- Skip consent screen for Android

#### Version Code: 24 Version Name: 1.1.2
- better error messages

#### Version Code: 22 Version Name: 1.1.1
- Update account deletion instruction to partner's email

#### Version Code: 21 Version Name: 1.1.0
- Use new nav

#### Version Code: 18 Version Name: 1.0.2
- Prettier login page
- Executive report page available

#### Version Code: 16 Version Name: 1.0.1
- Update Android app target to Android 14
- can not reuse approved version name

#### Version Code: 15 Version Name: 1.0.0
- Add delete account instruction

#### Version Code: 14 Version Name: 1.0.0
- Fix various UI bugs
- Remove Menu from Select location page

#### Version Code: 13 Version Name: 1.0.0
- Name back to 1.0.0 for release review
- Fix various UI bugs
- Notification bell available for SMB only user now
- Remove shop cart button
- Hide notification settings

#### Version Code: 9-12
- Apple automatically used these code during uploading, so skipping

#### Version Code: 8 Version Name: 1.2.1
- Use capacitor browser at login

#### Version Code: 6/7 Version Name: 1.1.0
- Deeplink notification

#### Version Code: 5 Version Name: 1.1.0
- Ask for notification permission

#### Version Code: 4 Version Name: 1.0.2
- Upload to testflight for external using same bundle as version 3

#### Version Code: 3 Version Name: 1.0.2
- Upload to testflight

#### Version Code: 2 Version Name: 1.0.1
- First Update

#### Version Code: 1 Version Name: 1.0.0
- Initial version
