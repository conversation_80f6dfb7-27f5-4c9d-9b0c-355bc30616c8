.header-layout {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  font-size: 14px;
  color: #666;

  sup {
    margin-left: 4px;
    color: #999;
  }
}

.multi-location-analytics {
  padding: 16px;

  .card-row {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}

// Import common brand styles
@import '../brands-common.component.scss';
