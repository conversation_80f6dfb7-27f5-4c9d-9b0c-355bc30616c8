@use 'design-tokens' as *;

.header-layout {
  @include text-preset-4--bold;
  margin-bottom: $spacing-2;
  margin-left: $spacing-2;
}

.multi-location-analytics {
  padding: 16px;

  .date-range-header {
    font-size: 14px;
    font-weight: 700;
    color: #000;
    margin-bottom: 12px;
    padding: 0;
    text-align: left;

    sup {
      margin-left: 2px;
      color: #000;
      font-size: 12px;
      font-weight: 700;
    }
  }

  .card-row {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}

// Import common brand styles
@import '../brands-common.component.scss';
