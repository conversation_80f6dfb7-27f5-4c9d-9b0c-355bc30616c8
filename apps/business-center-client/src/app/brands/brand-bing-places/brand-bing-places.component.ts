import { AsyncPipe } from '@angular/common';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit, signal } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { Observable, combineLatest, switchMap, zip } from 'rxjs';
import { filter, map, shareReplay, startWith } from 'rxjs/operators';
import { AccountGroup, getLocation } from '../../account-group/account-group';
import { LocationsService } from '../../locations';
import { AccountGroupMetricService } from '../../metrics/account-group.service';
import { BingPlacesService } from '../../metrics/bing-places.service';
import { NavigationService } from '../../navigation/navigation.service';
import { Mode, SidepanelService, Si<PERSON> } from '../../navigation/sidepanel.service';
import {
  CardDataContainer,
  CardMultiSourceDataConfig,
  MultiSeriesChartType,
} from '../../performance-dashboard/cards/interface';
import { ConnectedCardsService } from '../../performance-dashboard/connected-cards/connected-cards.service';
import { TimeRangePickerComponent } from '../../shared/time-range-picker.component';
import { TimeRangeService } from '../../shared/time-range.service';
import { CompareTab } from '../brand-compare/brand-compare.component';
import { BrandCompareModule } from '../brand-compare/brand-compare.module';
import { BrandFilterContainerModule } from '../brand-filter-container/brand-filter-container.module';
import { computeGradesFromMeasures } from '../grades';
import { BrandRow, MeasureValueMap, MetricColumn } from '../table/table.service';
import { MultiLocationService } from './../multi-location.service';
import { BrandBingPlacesSidebarComponent } from './brand-bing-places-sidebar/brand-bing-places-sidebar.component';
import { BrandBingPlacesSidebarModule } from './brand-bing-places-sidebar/brand-bing-places-sidebar.module';

@Component({
  selector: 'bc-brand-bing-places',
  templateUrl: './brand-bing-places.component.html',
  styleUrls: ['./brand-bing-places.component.scss', './../brands-common.component.scss'],
  imports: [
    AsyncPipe,
    BrandCompareModule,
    MatTooltipModule,
    GalaxyPageModule,
    TranslateModule,
    TimeRangePickerComponent,
    BrandFilterContainerModule,
    BrandBingPlacesSidebarModule,
  ],
})
export default class BrandBingPlacesComponent implements OnInit, OnDestroy {
  private readonly measureSelected = signal<string>('total_views');
  public readonly measureSelected$ = toObservable(this.measureSelected);
  compareTabs$: Observable<CompareTab[]>;
  insightsMeasureMap: Observable<{ [key: string]: MeasureValueMap }>;
  gradeMap$: Observable<{ [key: string]: string }>;
  mapLocations$: Observable<BrandRow[]>;
  loadedMapLocations$: Observable<BrandRow[]>;
  cardConfig$: Observable<CardMultiSourceDataConfig>;
  dataSource$: Observable<CardDataContainer>;

  tableColumns$: Observable<MetricColumn[]>;

  constructor(
    private nav: NavigationService,
    public multiLocationService: MultiLocationService,
    public sidepanelService: SidepanelService,
    private locationsService: LocationsService,
    public bingPlacesService: BingPlacesService,
    private timeRangeService: TimeRangeService,
    private accountGroupMetricService: AccountGroupMetricService,
    private connectedCardsService: ConnectedCardsService,
  ) {}

  ngOnInit(): void {
    this.sidepanelService.setView(Size.LARGE, Mode.SIDE, BrandBingPlacesSidebarComponent);
    this.connectedCardsService.forceMobile(true);
    this.multiLocationService.setMetricCategory('BING');

    this.nav.setBreadcrumbs([{ text: 'PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.BING_PLACES' }]);

    this.compareTabs$ = combineLatest([
      this.bingPlacesService.currentOverallInsights$,
      this.bingPlacesService.previousOverallInsights$,
    ]).pipe(
      map(([insights, pInsights]) => {
        if (!insights) {
          return [
            {
              titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING.TOTAL_VIEWS',
              measureKey: 'total_views',
            },
            {
              titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING.TOTAL_ACTIONS',
              measureKey: 'total_actions',
            },
          ];
        }
        let deltaViews: number, deltaActions: number;
        if (pInsights) {
          deltaViews = insights.totalViews() - pInsights.totalViews();
          deltaActions = insights.totalActions() - pInsights.totalActions();
        }
        const tabs = [];
        tabs.push({
          titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING.TOTAL_VIEWS',
          measureKey: 'total_views',
          value: insights.totalViews(),
          deltaAbs: deltaViews,
          deltaRel: 0,
          deltaGood: deltaViews > 0,
          showDeltaAbs: true,
        });
        tabs.push({
          titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING.TOTAL_ACTIONS',
          measureKey: 'total_actions',
          value: insights.totalActions(),
          deltaAbs: deltaActions,
          deltaRel: 0,
          deltaGood: deltaActions > 0,
          showDeltaAbs: true,
        });
        return tabs;
      }),
      startWith([
        {
          titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING.TOTAL_VIEWS',
          measureKey: 'total_views',
        },
        {
          titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING.TOTAL_ACTIONS',
          measureKey: 'total_actions',
        },
      ]),
    );

    // Build measure value map for Bing insights
    this.insightsMeasureMap = this.locationsService.currentAccountGroupIds$.pipe(
      switchMap((accountGroupIds) => {
        return zip([
          this.bingPlacesService.currentInsightsByLocation$,
          this.bingPlacesService.previousInsightsByLocation$,
        ]).pipe(
          map(([insightStats, pInsightStats]) => {
            const locationMeasureMap = {};
            if (accountGroupIds == null || insightStats == null) {
              return locationMeasureMap;
            }

            accountGroupIds.forEach((accountGroupId) => {
              // Default value all locations
              locationMeasureMap[accountGroupId] = {
                total_views: { value: null },
                total_actions: { value: null },
                map_views: { value: null },
                web_views: { value: null },
                actions_website: { value: null },
                actions_phone: { value: null },
                actions_directions: { value: null },
                actions_photo: { value: null },
                actions_located_at: { value: null },
                actions_review: { value: null },
                actions_menu: { value: null },
                actions_order_online: { value: null },
                actions_suggest_edit: { value: null },
                actions_others: { value: null },
              };
            });
            insightStats.forEach((insightStat) => {
              // Calculate "Others" as sum of order online, menu views, located at, reviews, and suggest edit
              const othersValue =
                insightStat.stat.actions_order_online +
                insightStat.stat.actions_menu +
                insightStat.stat.actions_located_at +
                insightStat.stat.actions_review +
                insightStat.stat.actions_suggest_edit;

              // Set value for all locations in stats
              locationMeasureMap[insightStat.dimension] = {
                total_views: { value: insightStat.stat.totalViews() },
                total_actions: { value: insightStat.stat.totalActions() },
                map_views: { value: insightStat.stat.mapViews() },
                web_views: { value: insightStat.stat.webViews() },
                actions_website: { value: insightStat.stat.actions_website },
                actions_phone: { value: insightStat.stat.actions_phone },
                actions_directions: { value: insightStat.stat.actions_directions },
                actions_photo: { value: insightStat.stat.actions_photo },
                actions_located_at: { value: insightStat.stat.actions_located_at },
                actions_review: { value: insightStat.stat.actions_review },
                actions_menu: { value: insightStat.stat.actions_menu },
                actions_order_online: { value: insightStat.stat.actions_order_online },
                actions_suggest_edit: { value: insightStat.stat.actions_suggest_edit },
                actions_others: { value: othersValue },
              };
            });
            if (pInsightStats) {
              pInsightStats.forEach((pInsightStat) => {
                locationMeasureMap[pInsightStat.dimension]['total_views'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['total_views'].value || 0) -
                  pInsightStat.stat.totalViews();
                locationMeasureMap[pInsightStat.dimension]['total_actions'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['total_actions'].value || 0) -
                  pInsightStat.stat.totalActions();
                locationMeasureMap[pInsightStat.dimension]['map_views'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['map_views'].value || 0) - pInsightStat.stat.mapViews();
                locationMeasureMap[pInsightStat.dimension]['web_views'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['web_views'].value || 0) - pInsightStat.stat.webViews();
                locationMeasureMap[pInsightStat.dimension]['actions_website'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['actions_website'].value || 0) -
                  pInsightStat.stat.actions_website;
                locationMeasureMap[pInsightStat.dimension]['actions_phone'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['actions_phone'].value || 0) -
                  pInsightStat.stat.actions_phone;
                locationMeasureMap[pInsightStat.dimension]['actions_directions'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['actions_directions'].value || 0) -
                  pInsightStat.stat.actions_directions;
                locationMeasureMap[pInsightStat.dimension]['actions_photo'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['actions_photo'].value || 0) -
                  pInsightStat.stat.actions_photo;
                locationMeasureMap[pInsightStat.dimension]['actions_located_at'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['actions_located_at'].value || 0) -
                  pInsightStat.stat.actions_located_at;
                locationMeasureMap[pInsightStat.dimension]['actions_review'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['actions_review'].value || 0) -
                  pInsightStat.stat.actions_review;
                locationMeasureMap[pInsightStat.dimension]['actions_menu'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['actions_menu'].value || 0) -
                  pInsightStat.stat.actions_menu;
                locationMeasureMap[pInsightStat.dimension]['actions_order_online'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['actions_order_online'].value || 0) -
                  pInsightStat.stat.actions_order_online;
                locationMeasureMap[pInsightStat.dimension]['actions_suggest_edit'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['actions_suggest_edit'].value || 0) -
                  pInsightStat.stat.actions_suggest_edit;

                // Calculate previous "Others" value for delta
                const previousOthersValue =
                  pInsightStat.stat.actions_order_online +
                  pInsightStat.stat.actions_menu +
                  pInsightStat.stat.actions_located_at +
                  pInsightStat.stat.actions_review +
                  pInsightStat.stat.actions_suggest_edit;

                locationMeasureMap[pInsightStat.dimension]['actions_others'].deltaAbs =
                  (locationMeasureMap[pInsightStat.dimension]['actions_others'].value || 0) - previousOthersValue;
              });
            }

            return locationMeasureMap;
          }),
        );
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    this.gradeMap$ = combineLatest([this.measureSelected$, this.insightsMeasureMap]).pipe(
      map(([measureSelected, measureMap]) => {
        if (measureMap == null) {
          return null;
        }
        const [locationGrades] = computeGradesFromMeasures(measureMap, measureSelected);
        return locationGrades;
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    // Progressively loaded data for map/table. Location data, then measures/grades
    this.mapLocations$ = combineLatest([
      this.accountGroupMetricService.filteredLocationsForPath$,
      this.insightsMeasureMap,
      this.gradeMap$,
    ]).pipe(
      map(([locations, bingMeasures, gradeMap]) => {
        if (locations == null) {
          return null;
        }

        return Object.keys(locations)
          .map((k) => locations[k])
          .map((ag: AccountGroup) => {
            return {
              accountGroup: ag,
              title: ag.companyName,
              subtitle: getLocation(ag),
              grade: (gradeMap || {})[ag.accountGroupId] || 'Loading',
              measureMap: (bingMeasures || {})[ag.accountGroupId] || {},
            };
          });
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    this.loadedMapLocations$ = this.mapLocations$.pipe(filter((locs) => !!locs));

    const chartConfig: CardMultiSourceDataConfig = {
      dataDisplayType: MultiSeriesChartType.LineWithFill,
      dataTitleTranslationKey: 'COMMON.TREND.12_MONTHS',
      chartConfig: {
        chartDataType: 'date',
        showLegend: true,
        colorStepOverride: 0,
        animations: false,
        yAxisTickFormatting: 'whole-number',
        formatting: 'day',
        zeroOnlyValuesAllowed: false,
      },
    };
    this.cardConfig$ = this.timeRangeService.formattingOption$.pipe(
      map((formatting) => {
        chartConfig.chartConfig.formatting = formatting;
        return chartConfig;
      }),
      startWith(chartConfig),
    );

    this.dataSource$ = combineLatest([this.measureSelected$, this.bingPlacesService.currentInsightsByTime$]).pipe(
      map(([measureSelected, statsByTime]) => {
        if (!statsByTime) {
          return {
            chartData: undefined,
          };
        }
        if (measureSelected === 'total_views') {
          return {
            chartData: [
              {
                title: 'PERFORMANCE.MULTI_LOCATION.BING.MAP_VIEW',
                data: statsByTime.map((statByTime) => {
                  return {
                    name: statByTime.localAlignedTime,
                    value: statByTime.stat.mapViews(),
                  };
                }),
              },
              {
                title: 'PERFORMANCE.MULTI_LOCATION.BING.WEB_VIEW',
                data: statsByTime.map((statByTime) => {
                  return {
                    name: statByTime.localAlignedTime,
                    value: statByTime.stat.webViews(),
                  };
                }),
              },
            ],
          };
        } else {
          return {
            chartData: [
              {
                title: 'PERFORMANCE.MULTI_LOCATION.BING.WEBSITE_ACTION',
                data: statsByTime.map((statByTime) => {
                  return {
                    name: statByTime.localAlignedTime,
                    value: statByTime.stat.actions_website,
                  };
                }),
              },
              {
                title: 'PERFORMANCE.MULTI_LOCATION.BING.PHONE_ACTION',
                data: statsByTime.map((statByTime) => {
                  return {
                    name: statByTime.localAlignedTime,
                    value: statByTime.stat.actions_phone,
                  };
                }),
              },
              {
                title: 'PERFORMANCE.MULTI_LOCATION.BING.DIRECTIONS_ACTION',
                data: statsByTime.map((statByTime) => {
                  return {
                    name: statByTime.localAlignedTime,
                    value: statByTime.stat.actions_directions,
                  };
                }),
              },
              {
                title: 'PERFORMANCE.MULTI_LOCATION.BING.PHOTO_VIEWS',
                data: statsByTime.map((statByTime) => {
                  return {
                    name: statByTime.localAlignedTime,
                    value: statByTime.stat.actions_photo,
                  };
                }),
              },
            ],
          };
        }
      }),
      startWith({
        chartData: undefined,
      }),
    );

    // Bing columns contain views and actions
    this.tableColumns$ = this.measureSelected$.pipe(
      map(() => {
        const columns = [];
        // Views columns
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING.TOTAL_VIEWS',
          measureKey: 'total_views',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING.MAP_VIEW',
          measureKey: 'map_views',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING.WEB_VIEW',
          measureKey: 'web_views',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });

        // Actions columns
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING.TOTAL_ACTIONS',
          measureKey: 'total_actions',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING.WEBSITES',
          measureKey: 'actions_website',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING.REQUEST_DIRECTIONS',
          measureKey: 'actions_directions',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING.PHONE_CALLS',
          measureKey: 'actions_phone',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING.PHOTO_VIEWS',
          measureKey: 'actions_photo',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });
        columns.push({
          headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING.OTHERS',
          measureKey: 'actions_others',
          metaValueList: ['mean', 'median', 'sum'],
          metaValueDefault: 'sum',
        });

        return columns;
      }),
    );
  }

  ngOnDestroy(): void {
    this.connectedCardsService.forceMobile(false);
    this.sidepanelService.clearView();
    this.sidepanelService.close();
  }

  onMeasureSelected(measureKey: string): void {
    this.measureSelected.set(measureKey);
  }
}
