import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ConnectedCardsModule } from '../../../performance-dashboard/connected-cards/connected-cards.module';
import { BrandSidebarHeaderModule } from '../../brand-sidebar-header/brand-sidebar-header.module';
import { BrandBingPlacesSidebarComponent } from './brand-bing-places-sidebar.component';

@NgModule({
  imports: [CommonModule, BrandSidebarHeaderModule, ConnectedCardsModule],
  exports: [BrandBingPlacesSidebarComponent],
  declarations: [BrandBingPlacesSidebarComponent],
})
export class BrandBingPlacesSidebarModule {}
