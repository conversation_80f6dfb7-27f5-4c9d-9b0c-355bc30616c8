import { Component, OnInit } from '@angular/core';
import { partnerId } from '../../../../globals';
import { TimeRangeService } from '../../../shared/time-range.service';

@Component({
  selector: 'bc-brand-bing-places-sidebar',
  templateUrl: './brand-bing-places-sidebar.component.html',
  styleUrls: ['./brand-bing-places-sidebar.component.scss'],
  standalone: false,
})
export class BrandBingPlacesSidebarComponent implements OnInit {
  partnerId = partnerId;
  startDate: Date;
  endDate: Date;

  constructor(private timeRangeService: TimeRangeService) {}

  ngOnInit(): void {
    this.timeRangeService.dateRange$.subscribe(([start, end]) => {
      this.startDate = start;
      this.endDate = end;
    });
  }

  cardLoaded(): void {
    // Handle card loaded event
    console.log('Bing card data loaded');
  }
}
