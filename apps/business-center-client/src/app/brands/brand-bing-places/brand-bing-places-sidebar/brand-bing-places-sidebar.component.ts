import { Component, OnInit } from '@angular/core';
import { partnerId } from '../../../../globals';
import { TimeRangeService } from '../../../shared/time-range.service';
import { BrandSidebarHeaderModule } from '../../brand-sidebar-header/brand-sidebar-header.module';
import { ConnectedCardsModule } from '../../../performance-dashboard/connected-cards/connected-cards.module';

@Component({
  selector: 'bc-brand-bing-places-sidebar',
  templateUrl: './brand-bing-places-sidebar.component.html',
  styleUrls: ['./brand-bing-places-sidebar.component.scss'],
  standalone: true,
  imports: [BrandSidebarHeaderModule, ConnectedCardsModule],
})
export class BrandBingPlacesSidebarComponent implements OnInit {
  partnerId = partnerId;
  startDate: Date;
  endDate: Date;

  constructor(private timeRangeService: TimeRangeService) {}

  ngOnInit(): void {
    this.timeRangeService.dateRange$.subscribe(([startDate, endDate]) => {
      this.startDate = startDate;
      this.endDate = endDate;
    });
  }
}
