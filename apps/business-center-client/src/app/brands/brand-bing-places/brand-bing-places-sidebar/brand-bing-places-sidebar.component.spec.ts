import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { of } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';

import { BrandBingPlacesSidebarComponent } from './brand-bing-places-sidebar.component';
import { ConnectedCardsService } from '../../../performance-dashboard/connected-cards/connected-cards.service';
import { TimeRangeService } from '../../../shared/time-range.service';

describe('BrandBingPlacesSidebarComponent', () => {
  let component: BrandBingPlacesSidebarComponent;
  let fixture: ComponentFixture<BrandBingPlacesSidebarComponent>;
  let mockConnectedCardsService: jest.Mocked<ConnectedCardsService>;
  let mockTimeRangeService: jest.Mocked<TimeRangeService>;

  beforeEach(async () => {
    // Create mocks
    mockConnectedCardsService = {
      forceMobile: jest.fn(),
    } as any;

    mockTimeRangeService = {
      dateRange$: of([
        new Date('2025-05-04'),
        new Date('2025-06-02'),
      ]),
    } as any;

    await TestBed.configureTestingModule({
      declarations: [BrandBingPlacesSidebarComponent],
      imports: [
        NoopAnimationsModule,
        TranslateModule.forRoot(),
      ],
      providers: [
        { provide: ConnectedCardsService, useValue: mockConnectedCardsService },
        { provide: TimeRangeService, useValue: mockTimeRangeService },
        TranslateService,
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(BrandBingPlacesSidebarComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have reactive start and end dates from time range service', () => {
    fixture.detectChanges();

    expect(component.startDate()).toEqual(new Date('2025-05-04'));
    expect(component.endDate()).toEqual(new Date('2025-06-02'));
  });

  it('should render bc-exec-bing-card component', () => {
    fixture.detectChanges();

    const bingCard = fixture.debugElement.nativeElement.querySelector('bc-exec-bing-card');
    expect(bingCard).toBeTruthy();
  });

  it('should pass correct start and end dates to bc-exec-bing-card', () => {
    fixture.detectChanges();

    const bingCard = fixture.debugElement.nativeElement.querySelector('bc-exec-bing-card');
    expect(bingCard).toBeTruthy();
    expect(bingCard.getAttribute('ng-reflect-start-date')).toBeTruthy();
    expect(bingCard.getAttribute('ng-reflect-end-date')).toBeTruthy();
  });

  it('should have correct CSS class for hiding title', () => {
    fixture.detectChanges();

    // The CSS is applied globally via SCSS, so we just verify the component renders
    expect(component).toBeTruthy();
  });

  it('should handle time range changes reactively', () => {
    // Test that the component correctly uses signals to react to time range changes
    // Since we're using inject() and toSignal(), the component will automatically
    // react to changes in the TimeRangeService.dateRange$ observable
    fixture.detectChanges();

    expect(component.startDate()).toEqual(new Date('2025-05-04'));
    expect(component.endDate()).toEqual(new Date('2025-06-02'));
  });

  it('should render bc-exec-bing-card component correctly', () => {
    fixture.detectChanges();

    const bingCard = fixture.debugElement.nativeElement.querySelector('bc-exec-bing-card');
    expect(bingCard).toBeTruthy();

    // Verify the component structure is correct
    expect(component).toBeTruthy();
  });

  it('should have proper component structure', () => {
    fixture.detectChanges();

    const componentElement = fixture.debugElement.nativeElement;
    expect(componentElement).toBeTruthy();
    
    const bingCard = componentElement.querySelector('bc-exec-bing-card');
    expect(bingCard).toBeTruthy();
  });
});
