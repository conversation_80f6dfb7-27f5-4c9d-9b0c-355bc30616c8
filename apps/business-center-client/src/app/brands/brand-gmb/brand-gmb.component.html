<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-title>{{ 'PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_MY_BUSINESS' | translate }}</glxy-page-title>
    <glxy-page-actions>
      <bc-time-range-picker></bc-time-range-picker>
      <bc-brand-filter-container></bc-brand-filter-container>
    </glxy-page-actions>
  </glxy-page-toolbar>
  <div
    class="header-layout"
    [matTooltip]="'Google Business Profile statistics are delayed by up to 5 days'"
    [matTooltipPosition]="'right'"
  >
    {{ gmbService.queryDateString$ | async }}
    <sup>*</sup>
  </div>
  <div class="multi-location-analytics">
    <div class="date-range-header">
      {{ gmbService.queryDateString$ | async }}
    </div>
    <div class="card-row">
      <bc-brand-compare
        [tabs]="compareTabs$ | async"
        [tableColumns$]="tableColumns$ | async"
        [brandRows]="loadedMapLocations$ | async"
        [measureSelected]="'total_views'"
        (measureSelectedChange)="onMeasureSelected($event)"
        [dataSource]="dataSource$ | async"
        [cardConfig]="cardConfig$ | async"
      ></bc-brand-compare>
    </div>
  </div>
</glxy-page>
