import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { PerformanceDashboardModule } from '../../performance-dashboard/performance-dashboard.module';
import { BrandTabsComponent } from '../brand-tabs/brand-tabs.component';
import { MapLegendModule } from '../map-legend/map-legend.module';
import { MapComponent } from '../map/map.component';
import { TableModule } from '../table/table.module';
import { BrandCompareComponent } from './brand-compare.component';
import { MatFormField, MatLabel, MatPrefix } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { ReactiveFormsModule } from '@angular/forms';

@NgModule({
  imports: [
    CommonModule,
    MapComponent,
    MapLegendModule,
    MatIconModule,
    MatTableModule,
    TableModule,
    BrandTabsComponent,
    TranslateModule,
    PerformanceDashboardModule,
    GalaxyAlertModule,
    MatFormField,
    MatInput,
    MatLabel,
    MatPrefix,
    ReactiveFormsModule,
  ],
  declarations: [BrandCompareComponent],
  exports: [BrandCompareComponent],
})
export class BrandCompareModule {}
