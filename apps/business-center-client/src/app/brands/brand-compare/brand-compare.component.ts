import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import { BehaviorSubject, combineLatest, map, Observable } from 'rxjs';
import { startWith, tap } from 'rxjs/operators';
import { SidepanelService } from '../../navigation/sidepanel.service';
import { CardDataContainer, CardMultiSourceDataConfig } from '../../performance-dashboard/cards/interface';
import { BrandSidebarHeaderService } from '../brand-sidebar-header/brand-sidebar-header.service';
import { BrandsService } from '../brands.service';
import { GradeFilter } from '../interface';
import { MultiLocationService } from '../multi-location.service';
import { BrandRow, MetricColumn } from '../table/table.service';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';

// TODO: Share number/change visualization from exec report
export interface CompareTab {
  // Title, Value, Change, Change Sentiment
  titleTranslateKey: string;
  value?: number;
  displayValueType?: string;
  changeDisplayValueType?: string;
  outOfValue?: number;
  deltaAbs?: number;
  deltaRel?: number;
  deltaGood?: boolean;
  showDeltaAbs?: boolean;
  // Context Control - on click, this will become the grades of the map / chart / table
  measureKey?: string;
  currentMeasure?: boolean; // indicates if the measure is current, not in time
  tooltipTranslateKey?: string;
}

@Component({
  selector: 'bc-brand-compare',
  templateUrl: './brand-compare.component.html',
  styleUrls: ['./brand-compare.component.scss'],
  encapsulation: ViewEncapsulation.None, // Override material tab styles
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class BrandCompareComponent implements OnInit, OnDestroy {
  // Tabs provide context to the rest of the visualizations in the card
  @Input()
  tabs: CompareTab[];
  // The visible table columns likely respond to the tab currently selected
  @Input()
  tableColumns$: MetricColumn[];
  @Input()
  tableColumnSaveKey: string;
  // The data under comparison -- graded locations with a bucket of measure data
  private brandRows$$ = new BehaviorSubject<BrandRow[]>([]);
  private _brandRows: BrandRow[] = [];
  @Input()
  set brandRows(rows: BrandRow[]) {
    this._brandRows = rows || [];
    this.brandRows$$.next(this._brandRows);
  }
  get brandRows(): BrandRow[] {
    return this._brandRows;
  }

  @Input()
  manageRouterLink: string;

  _dataSource: CardDataContainer;
  @Input()
  set dataSource(dataSource: CardDataContainer) {
    this.clearChartError();
    this._dataSource = Object.assign({}, dataSource);
  }
  @Input()
  cardConfig: CardMultiSourceDataConfig;
  private fb = inject(FormBuilder);
  private search$: Observable<string>;
  protected filteredBrandRows$: Observable<BrandRow[]>;
  @Input()
  measureSelected: string;
  @Output()
  measureSelectedChange = new EventEmitter<string>();

  chartError = false;
  chartErrorTranslationKey: string;

  // Filter
  gradeFilter$$ = new BehaviorSubject<GradeFilter>(GradeFilter.All);

  hasChildGroups$: Observable<boolean>;
  compare$$ = new BehaviorSubject<string>('TABLE.NODE_LOCATION');

  selectedIndex = 0;

  constructor(
    public multiLocationService: MultiLocationService,
    private brandsService: BrandsService,
    private sidebarHeaderService: BrandSidebarHeaderService,
    public sidepanelService: SidepanelService,
  ) {}
  protected form: FormGroup;
  ngOnInit(): void {
    this.setupFormAndSearch();
    // tapping stream so UI can manage subscription
    this.hasChildGroups$ = this.brandsService.hasChildGroups$.pipe(
      tap((hasGroups) => {
        if (hasGroups === false) {
          // If current node doesn't have child groups, move to locations if needed
          if (this.compare$$.getValue() === 'Group') {
            this.compare$$.next('TABLE.NODE_LOCATION');
          }
        }
      }),
    );
  }

  ngOnDestroy(): void {
    this.sidepanelService.close();
  }

  openDetailsDrawer(accountGroupId: string): void {
    this.sidebarHeaderService.setResource(accountGroupId);
    this.sidepanelService.open();
  }

  setCompare(compare: string): void {
    this.compare$$.next(compare);
    this.clearChartError();
  }

  onSelectedIndex(idx: number): void {
    this.selectedIndex = idx;
    this.measureSelected = this.tabs[idx].measureKey;
    this.measureSelectedChange.emit(this.measureSelected);
    this.clearChartError();
  }

  public onGradeSelectedChange(gradeFilter: GradeFilter): void {
    this.gradeFilter$$.next(gradeFilter);
  }

  private setupFormAndSearch(): void {
    this.form = this.fb.group({
      search: new FormControl<string>(''),
    });

    this.filteredBrandRows$ = combineLatest([
      this.brandRows$$,
      this.form.get('search')!.valueChanges.pipe(startWith('')),
    ]).pipe(
      map(([rows, search]) => rows.filter((row) => row?.title.toUpperCase().includes((search || '').toUpperCase()))),
    );
  }

  handleChartError(errorTranslationKey: string): void {
    this.chartErrorTranslationKey = errorTranslationKey;
    this.chartError = true;
  }

  clearChartError(): void {
    this.chartError = false;
    this.chartErrorTranslationKey = undefined;
  }
}
