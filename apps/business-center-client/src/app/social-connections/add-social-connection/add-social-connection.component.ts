import { Component, Inject, inject, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GA4PropertiesApiService } from '@vendasta/google-analytics';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import {
  BehaviorSubject,
  Observable,
  Subscription,
  combineLatest,
  filter,
  map,
  mapTo,
  merge,
  take,
  takeUntil,
  tap,
} from 'rxjs';
import { partnerId } from '../../../globals';
import { AccountGroupService } from '../../account-group';
import { GA4View, GAView, SocialPage } from '../interface';
import { SocialConnectionsService } from '../social-connections.service';
import { FeatureFlagService } from '../../core/feature-flag.service';
import { DOCUMENT } from '@angular/common';

const serviceType = {
  facebook: 'facebook',
  googleAnalytics: 'google_analytics',
  googleAnalyticsGA4: 'google_analyticsGA4',
  googleMyBusiness: 'google_my_business',
  googleSearchConsole: 'google_search_console',
};

const GOOGLE_USER_ID = 'gid';

@Component({
  selector: 'bc-add-social-connection',
  templateUrl: './add-social-connection.component.html',
  styleUrls: ['./add-social-connection.component.scss'],
  standalone: false,
})
export class AddSocialConnectionComponent implements OnInit, OnDestroy {
  private readonly featureFlag = inject(FeatureFlagService);

  @Input()
  serviceUserId: string;

  // nextRoutePath is the path to navigate to after a connection has been made (or attempted in some cases)
  @Input() nextRoutePath: string;

  accountGroupId: string;
  selectedAccount: string;
  accounts$: Observable<SocialPage[]>;
  pages$: Observable<SocialPage[]>;
  views$: Observable<GAView[]>;
  GA4views$: Observable<GA4View[]>;
  sites$: Observable<string[]>;

  loadingAccounts$: Observable<boolean>;
  loadingAccountsOverride = false;

  accountsCursor$: Observable<string>;
  hasAccountsCursor$: Observable<boolean>;
  pagesCursor$: Observable<string>;
  title: string;
  accountsSearchType: string;
  pagesSearchType: string;
  serviceType: string;
  hasAccounts: boolean;
  connecting: string = null;
  currentPage = 1;

  // googleUserId is used only for the google analytics service type
  googleUserId = '';

  subscriptions: Subscription[] = [];

  accountSearchText$$ = new BehaviorSubject<string>('');
  readonly accountSearchText$ = this.accountSearchText$$.asObservable();

  pageSearchText$$ = new BehaviorSubject<string>('');
  readonly pageSearchText$ = this.pageSearchText$$.asObservable();

  viewSearchText$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  readonly viewSearchText$: Observable<string> = this.viewSearchText$$.asObservable();

  @Input()
  set type(type: string) {
    switch (type) {
      case 'Google My Business':
        this.title = 'Google Business Profile';
        this.accountsSearchType = 'google_my_business_accounts';
        this.pagesSearchType = 'google_my_business_locations';
        this.serviceType = serviceType.googleMyBusiness;
        this.hasAccounts = true;
        break;
      case 'Facebook':
        this.title = 'Facebook';
        this.serviceType = serviceType.facebook;
        this.accountsSearchType = 'facebook';
        this.hasAccounts = false;
        break;
      case 'google-analyticsGA4':
        this.title = 'Google Analytics (GA4)';
        this.accountsSearchType = 'google_analytics_accounts';
        this.serviceType = serviceType.googleAnalyticsGA4;
        this.hasAccounts = false;
        break;
      case 'google-search-console':
        this.title = 'Google Search Console';
        this.accountsSearchType = 'google_search_console_accounts';
        this.serviceType = serviceType.googleSearchConsole;
        this.hasAccounts = false;
        break;
      default:
        this.title = type;
    }
  }

  constructor(
    private socialConnectionsService: SocialConnectionsService,
    private accountGroupService: AccountGroupService,
    private snackbarService: SnackbarService,
    private router: Router,
    private snowplowService: ProductAnalyticsService,
    private activatedRoute: ActivatedRoute,
    private ga: GA4PropertiesApiService,
    @Inject(DOCUMENT) private document: Document,
  ) {}

  ngOnInit(): void {
    this.loadingAccounts$ = this.socialConnectionsService.loadingAccounts$;
    this.accountsCursor$ = this.socialConnectionsService.accountsCursor$;
    this.hasAccountsCursor$ = this.accountsCursor$.pipe(map((c) => Boolean(c)));
    this.pagesCursor$ = this.socialConnectionsService.pagesCursor$;
    this.subscriptions.push(
      this.accountGroupService.currentAccountGroupId$.pipe(take(1)).subscribe((accountGroupId) => {
        this.accountGroupId = accountGroupId;
        switch (this.serviceType) {
          case serviceType.facebook:
            this.pagesSearchType = 'facebook';
            this.accounts$ = combineLatest([
              this.socialConnectionsService.getAllSocialPageInformation(
                this.accountGroupId,
                this.serviceUserId,
                this.pagesSearchType,
                null,
              ),
              this.pageSearchText$,
            ]).pipe(
              map(([res, search]) => {
                return res.filter((p) => p.name.toUpperCase().includes(search.toUpperCase()));
              }),
            );
            break;
          case serviceType.googleAnalyticsGA4:
            this.activatedRoute.queryParamMap
              .subscribe((paramMap) => {
                if (paramMap.has(GOOGLE_USER_ID)) {
                  this.googleUserId = paramMap.get(GOOGLE_USER_ID);
                  this.socialConnectionsService.getPropertiesInformation(this.googleUserId);
                  this.GA4views$ = combineLatest([this.socialConnectionsService.GA4views$, this.viewSearchText$]).pipe(
                    map(([views, search]) => {
                      if (views.length === 0) {
                        this.socialConnectionsService.noGaViews$$.next(true);
                        this.router.navigate([this.router.url.split('?')[0]]);
                      }
                      const searchUpper = search.toUpperCase();
                      return views.filter((v) => {
                        return (
                          v.accountName.toUpperCase().includes(searchUpper) ||
                          v.propertyName.toUpperCase().includes(searchUpper)
                        );
                      });
                    }),
                  );
                }
                // unsubscribe here because we only need to set the google user ID once
                // if we do not unsubscribe, the component receives an event for the path change
                // and the condition if (paramMap.has(GOOGLE_USER_ID)) is false
              })
              .unsubscribe();
            break;
          case serviceType.googleSearchConsole:
            this.activatedRoute.queryParamMap
              .subscribe((paramMap) => {
                if (paramMap.has(GOOGLE_USER_ID)) {
                  this.googleUserId = paramMap.get(GOOGLE_USER_ID);
                  this.socialConnectionsService.gscGetSitesInformation(this.googleUserId);
                  this.sites$ = combineLatest([this.socialConnectionsService.sites$, this.viewSearchText$]).pipe(
                    map(([sites, search]) => {
                      if (sites.length === 0) {
                        this.socialConnectionsService.noGaViews$$.next(true);
                        this.router.navigate([this.router.url.split('?')[0]]);
                      }
                      return sites.filter((s) => {
                        return s.toUpperCase().includes(search.toUpperCase());
                      });
                    }),
                  );
                }
                // unsubscribe here because we only need to set the google user ID once
                // if we do not unsubscribe, the component receives an event for the path change
                // and the condition if (paramMap.has(GOOGLE_USER_ID)) is false
              })
              .unsubscribe();
            break;
          default:
            this.socialConnectionsService.getSocialAccountInformation(
              accountGroupId,
              this.serviceUserId,
              this.accountsSearchType,
              null,
            );
            this.accounts$ = combineLatest([this.socialConnectionsService.accounts$, this.accountSearchText$]).pipe(
              map(([accounts, search]) => {
                return accounts.filter((a) => a.name.toLowerCase().includes(search.toLowerCase()));
              }),
              tap((accounts) => {
                if (accounts.length === 1) {
                  this.selectedAccount = accounts[0].accountId;
                }
              }),
            );
            break;
        }
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.map((sub) => sub.unsubscribe());
  }

  searchAccounts(): void {
    const accountsLengthGreaterThan1$ = this.accounts$.pipe(
      filter((accounts) => accounts.length >= 1),
      mapTo(true),
    );
    const cursorDone$ = this.socialConnectionsService.accountsCursor$.pipe(
      filter((cursor) => cursor === null || cursor === ''),
    );
    const accountsLengthGreaterThan1OrCursorDone$ = merge(accountsLengthGreaterThan1$, cursorDone$);
    this.loadingAccountsOverride = true;
    this.socialConnectionsService.accountsCursor$
      .pipe(
        tap((cursor) => this.loadMoreAccounts(cursor)),
        takeUntil(accountsLengthGreaterThan1OrCursorDone$),
      )
      .subscribe({
        complete: () => (this.loadingAccountsOverride = false),
      });
  }

  loadMoreAccounts(cursor: string): void {
    this.socialConnectionsService.getSocialAccountInformation(
      this.accountGroupId,
      this.serviceUserId,
      this.accountsSearchType,
      cursor,
    );
  }

  loadPanelData(accountId: string): void {
    this.selectedAccount = accountId;
    const socialPages = this.socialConnectionsService.getAllSocialPageInformation(
      this.accountGroupId,
      this.serviceUserId,
      this.pagesSearchType,
      null,
      accountId,
    );
    this.pageSearchChanged('');
    this.pages$ = combineLatest([socialPages, this.pageSearchText$]).pipe(
      map(([res, searchText]) => {
        return res.filter(
          (p) =>
            p.name.toLowerCase().includes(searchText.toLowerCase()) ||
            (p.address ? p.address.toLowerCase().includes(searchText.toLowerCase()) : false),
        );
      }),
    );
  }

  accountSearchChanged(s: string): void {
    this.accountSearchText$$.next(s);
  }

  pageSearchChanged(searchTerm: string): void {
    this.pageSearchText$$.next(searchTerm);
    this.currentPage = 1;
  }

  connectLocation(pageId: string): void {
    this.connecting = pageId;
    this.socialConnectionsService
      .connectSocialPage(this.accountGroupId, this.serviceUserId, this.serviceType, pageId)
      .subscribe((response) => {
        if (response.statusCode !== 200) {
          this.snackbarService.openErrorSnack(response.message ?? 'failed to connect with code ' + response.statusCode);
        }
        this.router.navigateByUrl(
          this.nextRoutePath ?? `/account/location/${this.accountGroupId}/settings/integrations`,
        );
      });
  }

  viewSearchChanged(searchTerm: string): void {
    this.viewSearchText$$.next(searchTerm);
    this.currentPage = 1;
  }

  connectGA4Property(
    accountId: string,
    propertyId: string,
    accountName: string,
    category: number,
    propertyName: string,
  ): void {
    this.connecting = propertyId;
    // by the time we are ready to connect a view, the googleUserId is already set
    this.socialConnectionsService.connectGA4Prop(
      partnerId,
      this.accountGroupId,
      accountId,
      propertyId,
      this.googleUserId,
      category,
      'CATEGORY_WEBSITE',
      propertyName,
      accountName,
    );
    const url = this.document.location.href;
    const urlParams = new URLSearchParams(new URL(url).search);
    const returnUrl = urlParams.get('returnUrl');
    if (returnUrl?.startsWith('https://')) {
      this.document.location.href = returnUrl;
    } else {
      this.router.navigateByUrl(`/account/location/${this.accountGroupId}/settings/integrations`);
    }
  }
  gscConnectSite(site: string): void {
    this.connecting = site;
    // by the time we are ready to connect a view, the googleUserId is already set
    this.socialConnectionsService
      .gscConnectSite(partnerId, this.accountGroupId, this.googleUserId, site)
      .subscribe((response) => {
        if (response.data === false) {
          this.snackbarService.openErrorSnack('failed to connect');
          return;
        }
        this.router.navigateByUrl(`/account/location/${this.accountGroupId}/settings/integrations`);
      });
  }
}
