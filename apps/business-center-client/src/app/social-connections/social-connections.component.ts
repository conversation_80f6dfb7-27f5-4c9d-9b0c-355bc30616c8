import { HttpErrorResponse } from '@angular/common/http';
import { Component, computed, DestroyRef, inject, OnDestroy, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { EnvironmentService } from '@galaxy/core';
import { IntegrationConnectionResponseList } from '@vendasta/api-gateway';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  Category,
  ConnectedProperty,
  GA4PropertiesApiService,
  LookupPropertiesRequest,
  LookupPropertiesResponse,
} from '@vendasta/google-analytics';
import { InstagramApiService } from '@vendasta/instagram';
import {
  MeetingSource,
  MeetingSourceInfo,
  MeetingSourceListResponse,
  MeetingSourceQuery,
  MeetingSourceStatus,
  MeetingsService,
} from '@vendasta/meetings';
import { Company, QuickBooksService } from '@vendasta/quickbooks';
import { catchError, combineLatest, delay, map, Observable, of, shareReplay, switchMap } from 'rxjs';
import { partnerId } from '../../globals';
import { AccountGroupService } from '../account-group/account-group.service';
import { AppConfigService } from '../app-config.service';
import { AuthService } from '../auth.service';
import { FeatureFlagService } from '../core/feature-flag.service';
import { PartnerMarketConfigService } from '../partner-market-config';
import { SocialService } from './interface';
import { SocialConnectionLinks, SocialConnections, SocialConnectionsService } from './social-connections.service';

type ConnectionType = 'social_connection' | 'instagram' | null;

@Component({
  selector: 'bc-social-connections',
  templateUrl: './social-connections.component.html',
  styleUrls: ['./social-connections.component.scss'],
  standalone: false,
})
export class SocialConnectionsComponent implements OnInit, OnDestroy {
  private readonly route = inject(ActivatedRoute);
  private readonly socialConnectionsService = inject(SocialConnectionsService);
  private readonly accountGroupService = inject(AccountGroupService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly router = inject(Router);
  private readonly meetingsService = inject(MeetingsService);
  private readonly featureFlag = inject(FeatureFlagService);
  private readonly quickBooksService = inject(QuickBooksService);
  private readonly partnerMarketConfigService = inject(PartnerMarketConfigService);
  private readonly appConfigService = inject(AppConfigService);
  private readonly ga = inject(GA4PropertiesApiService);
  private readonly authService = inject(AuthService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly instagramService = inject(InstagramApiService);
  private readonly environmentService = inject(EnvironmentService);

  protected mainSources$: Observable<SocialConnectionLinks>;
  protected connections$: Observable<SocialConnections>;
  protected addingSocialConnection = false;
  protected socialConnectionType: string;
  protected serviceUserId: string;
  protected postConnectionCreatedPath: string;

  protected readonly instagramUsers$: Observable<SocialService[]>;
  protected readonly hasQuickBooksConnectorAccess$ = this.partnerMarketConfigService.businessAppConfig$.pipe(
    map((conf) => !conf.hideQuickbooksConnector),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  private readonly featFlags$ = this.featureFlag
    .checkFeatureFlagsMulti(partnerId, '', [
      'google_search_console',
      'gingr_connection',
      'show_platform_integrations',
      'inbox_instagram_messages_integration',
    ])
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  protected readonly searchConsoleAccess$ = this.featFlags$.pipe(map((val) => val.google_search_console));
  protected readonly gingrAccess$ = this.featFlags$.pipe(map((val) => val.gingr_connection));
  protected readonly showPlatformIntegrations$ = combineLatest([this.route.queryParams, this.featFlags$]).pipe(
    map(([queryParams, featureFlag]) => {
      return featureFlag.show_platform_integrations && this.dictHasRequiredParams(queryParams) === null;
    }),
  );
  protected readonly showInstagramCard$ = this.featFlags$.pipe(map((val) => val.inbox_instagram_messages_integration));

  protected readonly showNoGaViewWarning$ = this.socialConnectionsService.noGaViews$.pipe(delay(0));
  protected readonly appName$ = this.appConfigService.businessAppName$;
  protected readonly impersonatedUser = this.authService.isImpersonating;
  protected readonly previousPageUrl = computed(
    () => `/account/location/${this.accountGroupService.currentAccountGroupId()}/administration`,
  );

  private connectionId: string;
  protected readonly instagramAuthUrl: string;
  private meetingSources$: Observable<MeetingSourceInfo[]>;
  private getGingrConnection$: Observable<IntegrationConnectionResponseList>;
  private google_analyticsGA4$: Observable<ConnectedProperty[]>;
  private readonly currentLocation = this.accountGroupService.currentAccountGroupId;

  constructor() {
    const agid = this.accountGroupService.currentAccountGroupId();
    const nextUrl: URL = new URL(`${window.location.protocol}//${window.location.host}`);
    const env = this.environmentService.getEnvironmentString();
    nextUrl.pathname = `/account/location/${agid}/settings/connections`;

    this.instagramAuthUrl =
      `https://instagram-${env}.apigateway.co/v2/auth-user` +
      `?partnerID=${partnerId}` +
      `&businessID=${agid}` +
      `&nextURL=${encodeURIComponent(nextUrl.href)}`;

    this.instagramUsers$ = this.instagramService
      .listUsers({
        partnerId: partnerId,
        businessId: agid,
        options: {
          pageSize: 5,
        },
      })
      .pipe(
        map((response) => response.users || []),
        map((users) =>
          users.map(
            (user) =>
              <SocialService>{
                type: 'instagram',
                displayName: user.username,
                name: user.fullName,
                ssid: user.userId,
                isVerified: true,
                pages: [],
                profileUrl: `https://www.instagram.com/${user.username}/`,
                profileImageUrl: user.profilePicture,
                authUrl: this.instagramAuthUrl,
                tokenIsBroken: user.tokenBroken,
              },
          ),
        ),
      );
  }

  ngOnInit(): void {
    this.getGingrConnection$ = this.socialConnectionsService
      .getIntegrationConnection(this.currentLocation(), 'gingrApp')
      .pipe(
        catchError((err: HttpErrorResponse) => {
          console.error('error querying getIntegrationConnection: ', err);
          return of([]);
        }),
        map((resp: IntegrationConnectionResponseList) => {
          return resp;
        }),
      );

    this.meetingSources$ = this.meetingsService
      .list([
        new MeetingSourceQuery({
          source: MeetingSource.MEETING_SOURCE_ZOOM,
          connectNextUrl: window.location.href,
          disconnectNextUrl: window.location.href,
        }),
        new MeetingSourceQuery({
          source: MeetingSource.MEETING_SOURCE_GOOGLE_MEET,
          connectNextUrl: window.location.href,
          disconnectNextUrl: window.location.href,
        }),
      ])
      .pipe(
        catchError((err: HttpErrorResponse) => {
          console.error('error querying meeting sources: ', err);
          return of([]);
        }),
        map((resp: MeetingSourceListResponse) => {
          return !resp || !resp.sourceInfo ? [] : resp.sourceInfo;
        }),
      );

    this.google_analyticsGA4$ = this.ga
      .lookupProperties(
        new LookupPropertiesRequest({
          partnerId,
          businessId: this.currentLocation(),
          category: Category.CATEGORY_WEBSITE,
        }),
      )
      .pipe(
        catchError((err: HttpErrorResponse) => {
          console.error('error querying meeting sources: ', err);
          return of([]);
        }),
        map((resp: LookupPropertiesResponse) => {
          return resp.properties;
        }),
      );

    const observableToQuickBooksRedirectURL$: Observable<string> = combineLatest([
      this.hasQuickBooksConnectorAccess$,
      this.accountGroupService.currentAccountGroupId$,
    ]).pipe(
      switchMap(([hasAccess, agid]) => {
        if (!hasAccess) {
          return of('');
        }

        return this.quickBooksService.getAuthorizationCodeRedirectUrl(agid, this.authService.userId());
      }),
      catchError((err) => {
        if (err.status >= 500) {
          this.snackbarService.openErrorSnack('SOCIAL_CONNECTIONS.QUICKBOOKS.LOADING_ERROR');
        }
        return of('');
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    const quickBooksConnectedCompany$ = this.hasQuickBooksConnectorAccess$.pipe(
      switchMap((hasAccess) => {
        if (!hasAccess) {
          return of(null);
        }
        return this.socialConnectionsService.quickbooksConnectedCompany$;
      }),
    );

    const agid$ = this.accountGroupService.currentAccountGroupId$;

    const authUrls$ = agid$.pipe(switchMap((agid) => this.socialConnectionsService.getAuthUrls(agid, window.location)));

    this.mainSources$ = combineLatest([
      authUrls$,
      this.meetingSources$,
      observableToQuickBooksRedirectURL$,
      agid$,
    ]).pipe(
      map(([sc, meetingSources, quickBooksAuthURL, agid]) => {
        sc.googleAnalyticsGA4 = this.socialConnectionsService.buildGoogleAnalyticsGA4AuthUrl(partnerId, agid);
        sc.googleSearchConsole = this.socialConnectionsService.buildGoogleSearchConsoleAuthUrl(partnerId, agid);
        meetingSources.forEach((meetingSource) => {
          if (meetingSource.source === MeetingSource.MEETING_SOURCE_ZOOM) {
            sc.zoom = meetingSource.connectDisconnectUrl;
          } else if (meetingSource.source === MeetingSource.MEETING_SOURCE_GOOGLE_MEET) {
            sc.google_meet = meetingSource.connectDisconnectUrl;
          }
        });
        sc.gingr = '';
        sc.quickbooks = quickBooksAuthURL;
        return sc;
      }),
    );

    const socialProfileConnections$ = this.socialConnectionsService.socialProfileConnections$;
    this.connections$ = combineLatest([
      socialProfileConnections$,
      this.meetingSources$,
      quickBooksConnectedCompany$,
      this.google_analyticsGA4$,
      this.getGingrConnection$,
    ]).pipe(
      map(([scs, meetingSources, quickBooksConnectedCompany, googleAnalyticsGA4, gingrConnectionRes]) => {
        return this.supplementConnections(
          scs,
          meetingSources,
          quickBooksConnectedCompany,
          googleAnalyticsGA4,
          gingrConnectionRes,
        );
      }),
    );

    combineLatest([this.route.queryParams, this.accountGroupService.currentAccountGroupId$])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([queryParams, agid]) => {
        this.addingSocialConnection = this.dictHasRequiredParams(queryParams) !== null;
        if (this.addingSocialConnection) {
          const target = this.route.snapshot.paramMap.get('target');
          this.postConnectionCreatedPath = `/account/location/${agid}/settings/${target ?? 'integrations'}`;
          const type = this.dictHasRequiredParams(queryParams);
          this.socialConnectionType = type === 'social_connection' ? queryParams.type : 'instagram';
          this.serviceUserId = queryParams.message;
          if (target === 'inbox' && this.socialConnectionType === 'instagram') {
            this.postConnectionCreatedPath += '?instagramUserId=' + this.serviceUserId;
          }
        } else if (queryParams.status === 'error') {
          this.router.navigateByUrl(`/account/location/${agid}/settings/connections`).then(() => {
            this.snackbarService.openErrorSnack('SETTINGS.SOCIAL.COULD_NOT_AUTH');
          });
        }
      });
  }

  ngOnDestroy(): void {
    this.socialConnectionsService.noGaViews$$.next(false);
  }

  protected createSocialConnection(location: string) {
    this.socialConnectionsService
      .createIntegrationConnection(this.currentLocation(), 'gingrApp', location)
      .subscribe(() => {
        this.snackbarService.openSuccessSnack('SETTINGS.SOCIAL.GINGR.SUCCESS_MESSAGE');
        this.reloadPage();
      });
  }

  protected updateSocialConnection(location: string) {
    this.socialConnectionsService
      .updateIntegrationConnection(this.currentLocation(), this.connectionId, location)
      .subscribe(() => {
        this.snackbarService.openSuccessSnack('SETTINGS.SOCIAL.GINGR.SUCCESS_UPDATE_MESSAGE');
        this.reloadPage();
      });
  }

  protected openGoogleAnalytics(): void {
    window.location.replace('https://analytics.google.com');
  }

  protected closeAlert(): void {
    this.socialConnectionsService.noGaViews$$.next(false);
  }

  protected openIntegrationsPage(): void {
    this.router.navigate([`/account/location/${this.currentLocation()}/settings/integrations`]);
  }

  private dictHasRequiredParams(checkingDict: Params): ConnectionType {
    const socialConnectionType =
      Object.prototype.hasOwnProperty.call(checkingDict, 'status') &&
      Object.prototype.hasOwnProperty.call(checkingDict, 'message') &&
      Object.prototype.hasOwnProperty.call(checkingDict, 'type') &&
      checkingDict.status !== 'error' &&
      checkingDict.status !== 'failed';
    const instagramConnectionType =
      Object.prototype.hasOwnProperty.call(checkingDict, 'message') &&
      Object.prototype.hasOwnProperty.call(checkingDict, 'status') &&
      !Object.prototype.hasOwnProperty.call(checkingDict, 'type');

    if (socialConnectionType) {
      return 'social_connection';
    } else if (instagramConnectionType) {
      return 'instagram';
    }
    return null;
  }

  private reloadPage() {
    window.location.reload();
  }

  private supplementConnections(
    scs: SocialConnections,
    meetingSources: MeetingSourceInfo[],
    quickBooksConnectedCompany: Company,
    googleAnalyticsGA4: ConnectedProperty[],
    gingrConnectionRes: IntegrationConnectionResponseList,
  ) {
    scs.zoom = [];
    scs.google_meet = [];
    scs.quickbooks = [];
    scs.google_analyticsGA4 = [];
    if (gingrConnectionRes?.connections?.length) {
      (this.connectionId = gingrConnectionRes.connections[0].connectionId),
        (scs.gingr = [
          {
            clientTags: [],
            deleteUrl: '',
            isVerified: false,
            name: 'Gingr connected',
            pages: [],
            ssid: gingrConnectionRes.connections[0].connectionId,
            tokenIsBroken: false,
            type: 'gingr',
            locationId: gingrConnectionRes.connections[0].data.locationId,
            mapsUri: '',
          },
        ]);
    }
    meetingSources.forEach((meetingSource) => {
      if (meetingSource.status === MeetingSourceStatus.MEETING_SOURCE_STATUS_CONNECTED) {
        if (meetingSource.source === MeetingSource.MEETING_SOURCE_ZOOM) {
          scs.zoom = [
            {
              clientTags: [],
              deleteUrl: '',
              isVerified: false,
              name: 'Zoom connected',
              pages: [],
              ssid: '',
              tokenIsBroken: false,
              type: 'zoom',
              locationId: '',
              mapsUri: '',
            },
          ];
        } else if (meetingSource.source === MeetingSource.MEETING_SOURCE_GOOGLE_MEET) {
          scs.google_meet = [
            {
              clientTags: [],
              deleteUrl: meetingSource.connectDisconnectUrl,
              isVerified: false,
              name: 'Google Meet connected',
              pages: [],
              ssid: '',
              tokenIsBroken: false,
              type: 'google_meet',
              locationId: '',
              mapsUri: '',
            },
          ];
        }
      }
    });
    if (googleAnalyticsGA4 != undefined) {
      scs.google_analyticsGA4 = [
        {
          clientTags: [],
          deleteUrl: '',
          isVerified: false,
          name: googleAnalyticsGA4[0].propertyName,
          pages: [],
          ssid: googleAnalyticsGA4[0].propertyId,
          tokenIsBroken: googleAnalyticsGA4[0].isBroken,
          type: 'ga4_view',
          locationId: '',
          mapsUri: '',
        },
      ];
    }
    if (!!quickBooksConnectedCompany && !!quickBooksConnectedCompany.name) {
      scs.quickbooks = [
        {
          clientTags: [],
          deleteUrl: '',
          isVerified: false,
          name: quickBooksConnectedCompany.name,
          pages: [],
          ssid: '',
          tokenIsBroken: false,
          type: 'quickbooks',
          locationId: '',
          mapsUri: '',
        },
      ];
    }
    return scs;
  }
}
