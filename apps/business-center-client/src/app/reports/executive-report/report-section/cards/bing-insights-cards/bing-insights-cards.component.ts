import { Component, OnInit } from '@angular/core';
import { combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';
import { ImageService } from '../../../../../core/image.service';
import { LocationsService, isBrand } from '../../../../../locations';
import {
  CardConfig,
  CardDataContainer,
  MultiLocationCardSourceConfig,
} from '../../../../../performance-dashboard/cards/interface';
import { ExecutiveReportQueryService, ReportType } from '../../../executive-report-query.service';
import { ActionsMetricsConfig, ViewsMetricsConfig } from './bing-insights-cards.config';
import { BingInsightsCardsService } from './bing-insights-cards.service';
import { of } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Component({
  selector: 'bc-bing-insights-cards',
  templateUrl: './bing-insights-cards.component.html',
  styleUrls: ['./bing-insights-cards.component.scss'],
  standalone: false,
})
export class BingInsightsCardsComponent implements OnInit {
  viewsMetricsConfig: CardConfig = ViewsMetricsConfig;
  viewsMetricsData: CardDataContainer;
  actionsMetricsConfig: CardConfig = ActionsMetricsConfig;
  actionsMetricsData: CardDataContainer;
  insightsData$: any;

  constructor(
    private bingInsightsCardsService: BingInsightsCardsService,
    private executiveReportQueryService: ExecutiveReportQueryService,
    private imageService: ImageService,
    private locationsService: LocationsService,
  ) {}

  ngOnInit(): void {
    const currentAndPreviousInsights$ = this.bingInsightsCardsService.getCurrentAndPreviousInsights().pipe(
      catchError((err) => {
        console.error('Error fetching current and previous insights:', err);
        return of({
          views: { chartData: [] },
          actions: { chartData: [] },
        });
      }),
    );

    const historicalViewInsights$ = this.bingInsightsCardsService.getHistoricalInsightsView().pipe(
      catchError((err) => {
        console.error('Error fetching historical view insights:', err);
        return of({ views: [] });
      }),
    );

    const historicalActionInsights$ = this.bingInsightsCardsService.getHistoricalInsightsAction().pipe(
      catchError((err) => {
        console.error('Error fetching historical action insights:', err);
        return of({ actions: [] });
      }),
    );

    const locationCount$ = this.bingInsightsCardsService.getDataLocationCount().pipe(
      catchError((err) => {
        console.error('Error fetching location count:', err);
        return of(0);
      }),
    );

    const reportType$ = this.executiveReportQueryService.typeOfReport$.pipe(
      catchError((err) => {
        console.error('Error fetching report type:', err);
        return of(ReportType.MULTI_LOCATION);
      }),
    );

    const currentLocation$ = this.locationsService.currentLocation$.pipe(
      catchError((err) => {
        console.error('Error fetching current location:', err);
        return of(null);
      }),
    );

    this.insightsData$ = combineLatest([
      currentAndPreviousInsights$,
      historicalViewInsights$,
      historicalActionInsights$,
      locationCount$,
      reportType$,
      currentLocation$,
    ]).pipe(
      map(([currentData, allTimeDataView, allTimeDataAction, count, reportType, brand]) => {
        const footerConfig: MultiLocationCardSourceConfig = {
          locationCount: count,
          linkTextKey: 'PERFORMANCE.LISTINGS.BING_INSIGHTS.TITLE',
          sourceIconUrl: this.imageService.getImageSrc('bing-logo.svg'),
        };

        if (reportType === ReportType.MULTI_LOCATION && isBrand(brand)) {
          footerConfig.linkUrl = `/account/brands/${brand.groupNodes[0]}/analytics/bing`;
        }

        this.viewsMetricsConfig.headingConfig.sourceConfig = footerConfig;
        this.actionsMetricsConfig.headingConfig.sourceConfig = footerConfig;

        currentData.views.chartData = [...(allTimeDataView.views || [])];
        currentData.actions.chartData = [...(allTimeDataAction.actions || [])];
        this.viewsMetricsData = { ...currentData.views };
        this.actionsMetricsData = { ...currentData.actions };
      }),
    );
  }
}
