import { CommonModule } from '@angular/common';
import { Component, EventEmitter, HostListener, Inject, InjectionToken, Input, Output } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';

import { combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';

import { SessionService } from '@galaxy/core';

import { DomainService } from '@vendasta/domain';
import { GetDomainByIdentifierResponse } from '@vendasta/domain/lib/_internal/objects';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';

import { environment, partnerId } from '../../../globals';
import { AccountGroup } from '../../account-group';
import { Brand } from '../../brands/brand';
import { LocationsService } from '../../locations';
import { Router } from '@angular/router';

type Data = {
  postId?: string;
  workflow?: string;
};

export const DATA_TOKEN = new InjectionToken<Data>('composer data token');

const PID_HOSTS_DEMO = new Map<string, string>();
PID_HOSTS_DEMO.set('vuni', 'https://socmktg-demo.appspot.com');

const PID_HOSTS_PROD = new Map<string, string>();

@Component({
  selector: 'bc-social-composer-composer',
  imports: [CommonModule],
  templateUrl: './composer.component.html',
  styleUrls: ['./composer.component.scss'],
})
export class ComposerComponent {
  @Input() workflow: string;
  @Output() composerClose: EventEmitter<void> = new EventEmitter();

  private readonly iframeHost$ = this.domainService
    .getDomainByIdentifier(`/application/SM/partner/${partnerId}`)
    .pipe(map((host) => this.iframeHost(host)));

  private readonly iframePath$ = combineLatest([
    this.locationsService.currentLocation$,
    this.sessionService.getSessionId(),
  ]).pipe(
    map(([loc, session]) =>
      loc instanceof Brand
        ? this.brandPath(this.data.postId, loc, session, this.workflow)
        : this.accountGroupPath(this.data.postId, loc),
    ),
  );

  public readonly trustedUrl$ = combineLatest([this.iframeHost$, this.iframePath$]).pipe(
    map(([host, path]) => this.sanitizer.bypassSecurityTrustResourceUrl(`${host}${path}`)),
  );

  constructor(
    @Inject(DATA_TOKEN) private readonly data: Data,
    private readonly sanitizer: DomSanitizer,
    private readonly locationsService: LocationsService,
    private readonly sessionService: SessionService,
    private readonly domainService: DomainService,
    private readonly snackbarService: SnackbarService,
    private router: Router,
  ) {}

  @HostListener('window:message', ['$event'])
  private handleWindowMessageEvent(event: MessageEvent) {
    switch (event?.data) {
      case 'Post Submitted':
        this.snackbarService.openSuccessSnack('BRANDS.POST_SUCCESS');
        break;
      case 'Post Scheduled':
        this.snackbarService.openSuccessSnack('BRANDS.POST_SCHEDULED');
        break;
      case 'Edit Submitted':
        this.snackbarService.openSuccessSnack('BRANDS.POST_EDIT');
        break;
      case 'Preview Edited':
        this.router.navigate([{ outlets: { action: ['compose-social-post'] } }]);
        break;
      case 'Composer Closed':
        this.closeComposer();
        break;
    }
  }

  private closeComposer() {
    this.composerClose.emit();
  }

  // Get the host of the composer iframe.
  private iframeHost(host: GetDomainByIdentifierResponse): string {
    return (
      (environment === 'prod' ? PID_HOSTS_PROD.get(partnerId) : PID_HOSTS_DEMO.get(partnerId)) ||
      `https://${host.primary.domain}`
    );
  }

  // Gets the brand path for the composer iframe.
  private brandPath(postId: string, brand: Brand, session: string, workflow: string): string {
    return (
      `/embed/brand/${partnerId}/${brand.groupNodes[0]}/compose?showClose=true&session_id=${session}` +
      (postId ? `&multilocationId=${postId}` : '') +
      (workflow ? `&workflow=${workflow}` : '')
    );
  }

  // The single location URL does not have the session attached, because the SL embed link throws an error for the
  // query param being too long. This means that the regular SSO flow occurs, which is desired. However, on
  // Safari, if the user has NEVER visited the SM domain directly, the SSO flow will fail. This is because Safari
  // does not allow 3rd party cookies to be set on the first visit to a domain via iframe. The user must visit the
  // SM domain directly first, and then the SSO flow will work.
  private accountGroupPath(postId: string, loc: AccountGroup): string {
    return `/embed/account/${loc.accountGroupId}/compose` + (postId ? '/' + postId : '' + '?showClose=true');
  }
}
