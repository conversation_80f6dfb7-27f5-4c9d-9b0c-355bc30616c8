@use 'design-tokens' as *;

.glxy-nav-panel {
  min-width: 275px;
}

::ng-deep .glxy-nav-panel-contents {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.glxy-product-nav-header {
  display: flex;
  align-items: center;
  padding: $spacing-4 $spacing-3;

  .app-logo {
    height: 32px;
    margin-right: $spacing-3;
  }

  .app-name {
    font-size: $font-preset-3-size;
    color: $secondary-font-color;
    font-weight: 500;
  }
}

.glxy-product-powered-by {
  background-color: white;
  padding: $spacing-2 $spacing-3 $spacing-4;
}

.powered-by-logo {
  max-height: 40px;
  max-width: 120px;
  object-fit: contain;
  height: auto;
  width: auto;
  display: block;
}

.powered-by-text {
  font-size: $font-preset-5-size;
  color: $glxy-grey-500;
}

@media print {
  ::ng-deep #product-navigation-bar-container,
  ::ng-deep mat-toolbar,
  ::ng-deep .mobile-action-bar {
    display: none !important;
  }
  ::ng-deep mat-sidenav-container,
  ::ng-deep mat-sidenav-content {
    height: auto !important;
  }
}

.flex {
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
}

.align-center {
  place-content: stretch center;
  align-items: stretch;
}

// Flex row/col + grid.
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.grid {
  display: grid;
}

// Flex-wrap utils.
.flex-wrap {
  flex-wrap: wrap;
} // Mostly used with flex-row, when wrapping is desired.
.flex-col-xs {
  @media screen and (max-width: 599px) {
    flex-direction: column;
  }
} // Switch from flex-row to flex-col on mobile.

// Flex/grow/shrink properties https://developer.mozilla.org/en-US/docs/Web/CSS/flex.
.flex-1 {
  flex: 1;
} // Same as flex: 1 1 0 (grow, shrink, basis 0). Has similar effect to width: 100%;
.flex-grow {
  flex-grow: 1;
} // Same as flex: 1 1 auto (grow, shrink, basis auto). For spacer, etc.

.admin-button-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-2;

  padding: $spacing-3;

  .admin-button {
    width: 100%;
  }
}
